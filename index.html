<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-with, initial-scale=1.0">
    <title>Southeast Texas Events</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        h1 {
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .subtitle {
            color: #7f8c8d;
            font-size: 1.2em;
        }

        .controls {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            align-items: center;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .search-box {
            flex: 1;
            min-width: 250px;
            padding: 12px 20px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .search-box:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .filter-select {
            padding: 12px 20px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            font-size: 16px;
            background: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .filter-select:focus {
            outline: none;
            border-color: #667eea;
        }

        .scrape-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .scrape-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .scrape-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .status {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: center;
            font-weight: bold;
            display: none;
        }

        .status.loading {
            background: rgba(52, 152, 219, 0.1);
            color: #3498db;
            display: block;
        }

        .status.success {
            background: rgba(46, 204, 113, 0.1);
            color: #27ae60;
            display: block;
        }

        .status.error {
            background: rgba(231, 76, 60, 0.1);
            color: #e74c3c;
            display: block;
        }

        .events-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 25px;
        }

        .event-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .event-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        .event-title {
            font-size: 1.4em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
            line-height: 1.3;
        }

        .event-date {
            color: #667eea;
            font-weight: bold;
            font-size: 1.1em;
            margin-bottom: 8px;
        }

        .event-location {
            color: #7f8c8d;
            margin-bottom: 12px;
            font-size: 0.95em;
        }

        .event-description {
            color: #555;
            line-height: 1.5;
            margin-bottom: 15px;
        }

        .event-category {
            display: inline-block;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: bold;
            text-transform: uppercase;
            margin-bottom: 10px;
        }

        .event-source {
            font-size: 0.8em;
            color: #95a5a6;
            text-align: right;
        }

        .event-link {
            display: inline-block;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            text-decoration: none;
            font-size: 0.9em;
            font-weight: bold;
            margin-top: 10px;
            transition: all 0.3s ease;
        }

        .event-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
            color: white;
            text-decoration: none;
        }

        .event-organizer {
            color: #667eea;
            font-weight: bold;
            font-size: 0.9em;
            margin-bottom: 5px;
        }

        .event-phone {
            color: #7f8c8d;
            font-size: 0.85em;
            margin-top: 5px;
        }

        .no-events {
            text-align: center;
            padding: 60px 20px;
            color: rgba(255, 255, 255, 0.8);
            font-size: 1.2em;
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }

            h1 {
                font-size: 2em;
            }

            .controls {
                flex-direction: column;
                align-items: stretch;
            }

            .search-box,
            .filter-select,
            .scrape-btn {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>Southeast Texas Events</h1>
            <p class="subtitle">Discover local events in Beaumont, Port Arthur, Orange & surrounding areas</p>
        </header>

        <div class="controls">
            <input type="text" id="searchBox" class="search-box" placeholder="Search events...">
            <select id="categoryFilter" class="filter-select">
                <option value="">All Categories</option>
                <option value="business">Business</option>
                <option value="entertainment">Entertainment</option>
                <option value="education">Education</option>
                <option value="sports">Sports</option>
                <option value="community">Community</option>
                <option value="arts">Arts & Culture</option>
                <option value="food">Food & Dining</option>
                <option value="health">Health & Wellness</option>
            </select>
            <button id="scrapeBtn" class="scrape-btn">Refresh Events</button>
        </div>

        <div id="status" class="status"></div>

        <div id="eventsGrid" class="events-grid">
            <!-- Events will be populated here -->
        </div>
    </div>

    <script>
        class SoutheastTexasEventScraper {
            constructor() {
                this.events = [];
                this.filteredEvents = [];
                this.apiBaseUrl = window.location.origin;
                this.init();
            }

            init() {
                this.setupEventListeners();
                this.loadRealEvents(); // Load real events from API
            }

            setupEventListeners() {
                document.getElementById('searchBox').addEventListener('input', () => this.filterEvents());
                document.getElementById('categoryFilter').addEventListener('change', () => this.filterEvents());
                document.getElementById('scrapeBtn').addEventListener('click', () => this.scrapeEvents());
            }

            async loadRealEvents() {
                try {
                    console.log('🔄 Loading real events from API...');
                    const url = `${this.apiBaseUrl}/api/events`;
                    console.log(`📡 Fetching from: ${url}`);

                    const response = await fetch(url);
                    console.log(`📊 Response status: ${response.status}`);

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    const responseText = await response.text();
                    console.log(`📄 Response text (first 200 chars): ${responseText.substring(0, 200)}`);

                    let data;
                    try {
                        data = JSON.parse(responseText);
                    } catch (parseError) {
                        console.error('❌ JSON parse error:', parseError);
                        console.error('📄 Full response text:', responseText);
                        throw new Error('Invalid JSON response from server');
                    }

                    console.log('📊 Parsed data:', data);
                    console.log(`📈 Events in response: ${data.events ? data.events.length : 'undefined'}`);

                    if (data.success && data.events) {
                        this.events = data.events;
                        this.filteredEvents = [...this.events];

                        console.log(`🎯 Events assigned: ${this.events.length}`);
                        console.log(`🎯 Filtered events: ${this.filteredEvents.length}`);
                        console.log('🎯 First event:', this.events[0]);

                        this.renderEvents();
                        console.log(`✅ Loaded and rendered ${this.events.length} real events`);

                        if (data.lastScrapeTime) {
                            this.showStatus(`Last updated: ${new Date(data.lastScrapeTime).toLocaleString()}`, 'success');
                        } else {
                            this.showStatus(`Loaded ${this.events.length} events successfully`, 'success');
                        }
                    } else {
                        console.warn('⚠️ No events in response or success=false');
                        throw new Error(data.error || 'No events found or API returned success=false');
                    }
                } catch (error) {
                    console.error('❌ Error loading events:', error);
                    this.showStatus('Error loading events. Using sample data.', 'error');
                    this.loadSampleEvents(); // Fallback to sample events
                }
            }

            // Sample events for demonstration
            loadSampleEvents() {
                this.events = [
                    {
                        title: "Golden Triangle Job Fair",
                        description: "Connect with local employers and explore career opportunities in the Golden Triangle area. Over 50 companies will be represented.",
                        date: "2025-06-20T10:00:00",
                        location: "Ford Park, Beaumont, TX",
                        category: "business",
                        organizer: "Workforce Solutions",
                        source: "beaumont_gov"
                    },
                    {
                        title: "Spindletop Gladys City Boomtown Festival",
                        description: "Annual festival celebrating the oil boom history of Southeast Texas with live music, food vendors, and historical exhibits.",
                        date: "2025-06-25T09:00:00",
                        location: "Spindletop-Gladys City Boomtown Museum, Beaumont, TX",
                        category: "entertainment",
                        organizer: "Beaumont CVB",
                        source: "beaumont_gov"
                    },
                    {
                        title: "Port Arthur Seafood Festival",
                        description: "Taste the best seafood in Southeast Texas! Live music, family activities, and fresh Gulf Coast cuisine.",
                        date: "2025-07-04T11:00:00",
                        location: "Port Arthur Civic Center, Port Arthur, TX",
                        category: "food",
                        organizer: "Port Arthur Chamber",
                        source: "portarthur_gov"
                    },
                    {
                        title: "Lamar University Summer Concert Series",
                        description: "Free outdoor concerts featuring local and regional artists. Bring your blankets and chairs!",
                        date: "2025-06-28T19:00:00",
                        location: "Lamar University Quad, Beaumont, TX",
                        category: "entertainment",
                        organizer: "Lamar University",
                        source: "lamar_edu"
                    },
                    {
                        title: "Orange County Business Expo",
                        description: "Network with local business leaders and discover new services and products in Orange County.",
                        date: "2025-07-10T08:00:00",
                        location: "Orange County Convention Center, Orange, TX",
                        category: "business",
                        organizer: "Orange Chamber of Commerce",
                        source: "orange_gov"
                    },
                    {
                        title: "Southeast Texas Art Walk",
                        description: "Monthly art walk featuring local galleries, artists, and live demonstrations in downtown Beaumont.",
                        date: "2025-06-15T17:00:00",
                        location: "Downtown Beaumont, TX",
                        category: "arts",
                        organizer: "Beaumont Art Society",
                        source: "beaumont_arts"
                    },
                    {
                        title: "Jefferson County Health Fair",
                        description: "Free health screenings, wellness information, and family activities promoting healthy living.",
                        date: "2025-07-12T09:00:00",
                        location: "Jefferson County Courthouse, Beaumont, TX",
                        category: "health",
                        organizer: "Jefferson County Health Dept",
                        source: "jefferson_county"
                    },
                    {
                        title: "Sabine Pass Lighthouse Tours",
                        description: "Guided tours of the historic Sabine Pass Lighthouse with maritime history presentations.",
                        date: "2025-06-22T10:00:00",
                        location: "Sabine Pass, TX",
                        category: "education",
                        organizer: "Texas Historical Commission",
                        source: "sabine_pass"
                    }
                ];

                this.filteredEvents = [...this.events];
                this.renderEvents();
            }

            async scrapeEvents() {
                const btn = document.getElementById('scrapeBtn');

                btn.disabled = true;
                btn.textContent = 'Scraping...';
                this.showStatus('Starting real web scraping of Southeast Texas events...', 'loading');

                try {
                    console.log('🔍 Starting real web scraping...');

                    // Trigger real web scraping via API
                    const scrapeResponse = await fetch(`${this.apiBaseUrl}/api/events/scrape`, {
                        method: 'POST'
                    });
                    const scrapeData = await scrapeResponse.json();

                    if (scrapeData.success) {
                        this.showStatus('Web scraping in progress... This may take a few minutes.', 'loading');

                        // Poll for completion
                        await this.pollScrapingStatus();

                        // Reload events after scraping
                        await this.loadRealEvents();

                        this.showStatus(`Successfully scraped ${this.events.length} real events!`, 'success');
                    } else {
                        throw new Error(scrapeData.error || 'Failed to start scraping');
                    }

                } catch (error) {
                    console.error('❌ Scraping error:', error);
                    this.showStatus('Error scraping events. Please try again.', 'error');
                } finally {
                    btn.disabled = false;
                    btn.textContent = 'Refresh Events';

                    setTimeout(() => {
                        document.getElementById('status').style.display = 'none';
                    }, 5000);
                }
            }

            async pollScrapingStatus() {
                const maxAttempts = 30; // 30 attempts = 1 minute
                let attempts = 0;

                while (attempts < maxAttempts) {
                    try {
                        const response = await fetch(`${this.apiBaseUrl}/api/events/status`);
                        const data = await response.json();

                        if (!data.isScrapingInProgress) {
                            console.log('✅ Scraping completed');
                            return;
                        }

                        console.log('⏳ Scraping still in progress...');
                        await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds
                        attempts++;

                    } catch (error) {
                        console.error('Error checking scraping status:', error);
                        break;
                    }
                }

                console.log('⚠️ Polling timeout reached');
            }

            showStatus(message, type) {
                const status = document.getElementById('status');
                status.className = `status ${type}`;
                status.textContent = message;
                status.style.display = 'block';
            }

            async simulateEventScraping() {
                // In a real implementation, this would make actual HTTP requests
                // to scrape various Southeast Texas event sources
                
                const sources = [
                    'Beaumont City Events',
                    'Port Arthur Events',
                    'Orange County Events',
                    'Lamar University Events',
                    'Jefferson County Events',
                    'Eventbrite Southeast Texas',
                    'Facebook Events'
                ];

                for (let source of sources) {
                    document.getElementById('status').textContent = `Scraping ${source}...`;
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }

                // Add some randomized new events
                const newEvents = [
                    {
                        title: "Southeast Texas Farmers Market",
                        description: "Fresh local produce, handmade crafts, and live music every Saturday morning.",
                        date: "2025-06-14T08:00:00",
                        location: "Downtown Beaumont, TX",
                        category: "community",
                        organizer: "Beaumont Farmers Market",
                        source: "scraped_beaumont"
                    },
                    {
                        title: "Neches River Cleanup Day",
                        description: "Community volunteer event to help keep our local waterways clean and beautiful.",
                        date: "2025-06-18T09:00:00",
                        location: "Riverfront Park, Beaumont, TX",
                        category: "community",
                        organizer: "Environmental Action Group",
                        source: "scraped_environmental"
                    }
                ];

                this.events = [...this.events, ...newEvents];
                this.filteredEvents = [...this.events];
                this.renderEvents();
            }

            filterEvents() {
                const searchTerm = document.getElementById('searchBox').value.toLowerCase();
                const categoryFilter = document.getElementById('categoryFilter').value;

                this.filteredEvents = this.events.filter(event => {
                    const matchesSearch = !searchTerm || 
                        event.title.toLowerCase().includes(searchTerm) ||
                        event.description.toLowerCase().includes(searchTerm) ||
                        event.location.toLowerCase().includes(searchTerm);
                    
                    const matchesCategory = !categoryFilter || event.category === categoryFilter;
                    
                    return matchesSearch && matchesCategory;
                });

                this.renderEvents();
            }

            renderEvents() {
                console.log('🎨 renderEvents called');
                const grid = document.getElementById('eventsGrid');
                console.log('🎨 Grid element:', grid);
                console.log(`🎨 Filtered events count: ${this.filteredEvents.length}`);

                if (!grid) {
                    console.error('❌ eventsGrid element not found!');
                    return;
                }

                if (this.filteredEvents.length === 0) {
                    console.log('⚠️ No events to display');
                    grid.innerHTML = '<div class="no-events">No events found matching your criteria.</div>';
                    return;
                }

                console.log('🎨 Creating event cards...');
                const eventCards = this.filteredEvents.map(event => this.createEventCard(event));
                console.log(`🎨 Created ${eventCards.length} event cards`);

                grid.innerHTML = eventCards.join('');
                console.log('✅ Events rendered to grid');
            }

            createEventCard(event) {
                const date = new Date(event.date);
                const formattedDate = date.toLocaleDateString('en-US', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                });
                const formattedTime = date.toLocaleTimeString('en-US', {
                    hour: 'numeric',
                    minute: '2-digit',
                    hour12: true
                });

                // Create event link if URL is available
                const eventLink = event.url ?
                    `<a href="${event.url}" target="_blank" class="event-link">View Event Details →</a>` : '';

                // Show organizer if available
                const organizer = event.organizer ?
                    `<div class="event-organizer">Organized by: ${event.organizer}</div>` : '';

                // Show phone if available
                const phone = event.phone ?
                    `<div class="event-phone">📞 ${event.phone}</div>` : '';

                return `
                    <div class="event-card">
                        <div class="event-category">${event.category}</div>
                        <h3 class="event-title">${event.title}</h3>
                        <div class="event-date">${formattedDate} at ${formattedTime}</div>
                        <div class="event-location">📍 ${event.location}</div>
                        ${organizer}
                        <div class="event-description">${event.description}</div>
                        ${phone}
                        ${eventLink}
                        <div class="event-source">Source: ${event.source}</div>
                    </div>
                `;
            }
        }

        // Initialize the application
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🚀 DOM loaded, initializing app...');
            try {
                const app = new SoutheastTexasEventScraper();
                console.log('✅ App initialized successfully');
                window.eventApp = app; // Make available for debugging
            } catch (error) {
                console.error('❌ Failed to initialize app:', error);
            }
        });
    </script>
</body>
</html>