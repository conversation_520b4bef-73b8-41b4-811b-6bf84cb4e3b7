const express = require('express');
const cors = require('cors');
const path = require('path');
require('dotenv').config();

const RealEventAgent = require('./src/realEventAgent');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('.'));

// Initialize real event agent
const realEventAgent = new RealEventAgent();

// Store the agent globally for routes
app.locals.eventAgent = realEventAgent;

// Routes
app.use('/api/events', require('./src/routes/realEventRoutes'));

// Serve the main HTML file
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({ 
        status: 'OK', 
        timestamp: new Date().toISOString(),
        mode: 'real-web-scraping'
    });
});

// Error handling middleware
app.use((err, req, res, next) => {
    console.error('Error:', err);
    res.status(500).json({ 
        error: 'Internal server error',
        message: err.message 
    });
});

// Start server
app.listen(PORT, () => {
    console.log(`🚀 Southeast Texas Events (REAL) server running on port ${PORT}`);
    console.log(`📱 Open http://localhost:${PORT} to view the application`);
    console.log(`🌐 This server uses REAL web scraping with actual web search and fetch`);
});

module.exports = app;
