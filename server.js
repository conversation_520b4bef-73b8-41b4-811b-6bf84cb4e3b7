const express = require('express');
const cors = require('cors');
const path = require('path');
require('dotenv').config();

// Import the actual web tools
const { webSearch, webFetch } = require('./src/realWebTools');
const WebScrapingService = require('./src/webScrapingService');
const EventEnhancer = require('./src/enhanced/eventEnhancer');
const eventRoutes = require('./src/routes/events');
const enhancedRoutes = require('./src/enhanced/routes');

const app = express();
const PORT = process.env.PORT || 8080;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('.'));

// Initialize web scraping service with REAL web tools
const webScrapingService = new WebScrapingService(webSearch, webFetch);

// Initialize enhanced event system
const eventEnhancer = new EventEnhancer();

// Make services available to routes
app.locals.eventScraper = webScrapingService;
app.locals.eventEnhancer = eventEnhancer;

// Routes
app.use('/api/events', eventRoutes);
app.use('/api/enhanced', enhancedRoutes);

// Venue routes
const PriorityVenuesDatabase = require('./src/enhanced/priorityVenues');

// GET /api/venues/:id - Get venue details
app.get('/api/venues/:id', (req, res) => {
    try {
        const priorityVenues = new PriorityVenuesDatabase();
        const venue = priorityVenues.getVenueById(req.params.id);

        if (!venue) {
            return res.status(404).json({
                success: false,
                error: 'Venue not found'
            });
        }

        res.json({
            success: true,
            venue
        });
    } catch (error) {
        console.error('Error fetching venue:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch venue',
            message: error.message
        });
    }
});

// POST /api/venues/:id/scrape - Scrape events for specific venue
app.post('/api/venues/:id/scrape', async (req, res) => {
    try {
        const venueId = req.params.id;
        const eventScraper = app.locals.eventScraper;
        const eventEnhancer = app.locals.eventEnhancer;

        console.log(`🎯 Scraping events for venue: ${venueId}`);

        // Get venue details to determine scraping URL
        const priorityVenues = new PriorityVenuesDatabase();
        const venue = priorityVenues.getVenueById(venueId);

        if (!venue) {
            return res.status(404).json({
                success: false,
                error: 'Venue not found'
            });
        }

        // Scrape events from the venue's website
        let scrapedEvents = [];

        if (venue.website) {
            console.log(`🌐 Scraping venue website: ${venue.website}`);

            // Create a mock search result for the venue website
            const searchResult = {
                title: venue.name,
                url: venue.website,
                snippet: `Events at ${venue.name}`
            };

            // Use the real web scraper to scrape this specific venue
            const RealWebScraper = require('./src/realWebScraper');
            const scraper = new RealWebScraper(
                eventScraper.webSearchTool.bind(eventScraper),
                eventScraper.webFetchTool.bind(eventScraper)
            );

            const venueEvents = await scraper.scrapeEventPage(searchResult);
            if (venueEvents && venueEvents.length > 0) {
                scrapedEvents = venueEvents;
            }
        }

        console.log(`✅ Found ${scrapedEvents.length} events for venue ${venue.name}`);

        // Enhance the scraped events and associate with this venue
        if (scrapedEvents.length > 0) {
            const enhancedEvents = await eventEnhancer.enhanceEventsFromOriginal(scrapedEvents);
            console.log(`✨ Enhanced ${enhancedEvents.length} events for venue`);
        }

        res.json({
            success: true,
            message: `Found ${scrapedEvents.length} events for ${venue.name}`,
            events: scrapedEvents.length,
            venue: venue.name
        });

    } catch (error) {
        console.error('Error scraping venue events:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to scrape venue events',
            message: error.message
        });
    }
});

// Serve the main HTML file
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Simple API test endpoint
app.get('/api/test', (req, res) => {
    res.json({
        success: true,
        message: 'API is working!',
        timestamp: new Date().toISOString()
    });
});

// Error handling middleware
app.use((err, req, res, next) => {
    console.error('Error:', err);
    res.status(500).json({ 
        error: 'Internal server error',
        message: err.message 
    });
});

// Start server
app.listen(PORT, () => {
    console.log(`🚀 Southeast Texas Events server running on port ${PORT}`);
    console.log(`📱 Open http://localhost:${PORT} to view the application`);
    
    // Start initial event scraping and enhancement
    console.log('🔍 Starting initial real web scraping...');
    webScrapingService.scrapeAllSources()
        .then(async (scrapedEvents) => {
            console.log(`✅ Scraping completed! Found ${scrapedEvents.length} events`);

            // Enhance the scraped events and associate with venues
            console.log('🎯 Enhancing scraped events and associating with venues...');
            const enhancedEvents = await eventEnhancer.enhanceEventsFromOriginal(scrapedEvents);
            console.log(`✨ Enhanced ${enhancedEvents.length} events with venue associations`);
        })
        .catch(console.error);
});

module.exports = app;
