const express = require('express');
const cors = require('cors');
const path = require('path');
require('dotenv').config();

const WebScrapingService = require('./src/webScrapingService');
const EventEnhancer = require('./src/enhanced/eventEnhancer');
const eventRoutes = require('./src/routes/events');
const enhancedRoutes = require('./src/enhanced/routes');

const app = express();
const PORT = process.env.PORT || 8080;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('.'));

// Initialize web scraping service
const webScrapingService = new WebScrapingService();

// Initialize enhanced event system
const eventEnhancer = new EventEnhancer();

// Make services available to routes
app.locals.eventScraper = webScrapingService;
app.locals.eventEnhancer = eventEnhancer;

// Routes
app.use('/api/events', eventRoutes);
app.use('/api/enhanced', enhancedRoutes);

// Serve the main HTML file
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Simple API test endpoint
app.get('/api/test', (req, res) => {
    res.json({
        success: true,
        message: 'API is working!',
        timestamp: new Date().toISOString()
    });
});

// Error handling middleware
app.use((err, req, res, next) => {
    console.error('Error:', err);
    res.status(500).json({ 
        error: 'Internal server error',
        message: err.message 
    });
});

// Start server
app.listen(PORT, () => {
    console.log(`🚀 Southeast Texas Events server running on port ${PORT}`);
    console.log(`📱 Open http://localhost:${PORT} to view the application`);
    
    // Start initial event scraping
    console.log('🔍 Starting initial real web scraping...');
    webScrapingService.scrapeAllSources().catch(console.error);
});

module.exports = app;
