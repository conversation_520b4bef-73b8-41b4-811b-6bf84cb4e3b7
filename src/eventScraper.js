const axios = require('axios');
const cheerio = require('cheerio');

class EventScraper {
    constructor() {
        this.events = [];
        this.isScrapingInProgress = false;
        this.lastScrapeTime = null;
        
        // Southeast Texas cities and areas to focus on
        this.targetAreas = [
            'Beaumont TX',
            'Port Arthur TX', 
            'Orange TX',
            'Nederland TX',
            'Vidor TX',
            'Lumberton TX',
            'Silsbee TX',
            'Bridge City TX',
            'Southeast Texas',
            'Golden Triangle Texas'
        ];
        
        // Event categories mapping
        this.categoryKeywords = {
            business: ['business', 'networking', 'conference', 'seminar', 'workshop', 'job fair', 'career'],
            entertainment: ['concert', 'music', 'festival', 'show', 'performance', 'comedy', 'theater'],
            education: ['class', 'workshop', 'seminar', 'lecture', 'training', 'course', 'school'],
            sports: ['game', 'tournament', 'race', 'sports', 'athletic', 'fitness', 'run', 'walk'],
            community: ['community', 'volunteer', 'charity', 'fundraiser', 'meeting', 'cleanup'],
            arts: ['art', 'gallery', 'museum', 'craft', 'painting', 'sculpture', 'exhibition'],
            food: ['food', 'restaurant', 'dining', 'cooking', 'festival', 'taste', 'culinary'],
            health: ['health', 'wellness', 'medical', 'fitness', 'yoga', 'meditation', 'screening']
        };
    }

    async scrapeAllSources() {
        if (this.isScrapingInProgress) {
            console.log('Scraping already in progress...');
            return { status: 'already_running', events: this.events };
        }

        this.isScrapingInProgress = true;
        console.log('🔍 Starting comprehensive event scraping for Southeast Texas...');
        
        try {
            const newEvents = [];
            
            // Search for events using multiple search strategies
            const searchQueries = [
                'events Beaumont Texas 2024 2025',
                'Port Arthur Texas events calendar',
                'Orange Texas community events',
                'Southeast Texas festivals concerts',
                'Golden Triangle Texas events',
                'Lamar University events Beaumont',
                'Jefferson County Texas events',
                'Beaumont civic center events',
                'Southeast Texas business events networking'
            ];

            for (const query of searchQueries) {
                console.log(`🔍 Searching: ${query}`);
                try {
                    const searchResults = await this.searchForEvents(query);
                    newEvents.push(...searchResults);
                    
                    // Add delay between searches to be respectful
                    await this.delay(2000);
                } catch (error) {
                    console.error(`Error searching for "${query}":`, error.message);
                }
            }

            // Remove duplicates and filter for quality
            const uniqueEvents = this.removeDuplicates(newEvents);
            const filteredEvents = this.filterQualityEvents(uniqueEvents);
            
            this.events = filteredEvents;
            this.lastScrapeTime = new Date();
            
            console.log(`✅ Scraping completed! Found ${this.events.length} unique events`);
            
            return {
                status: 'success',
                events: this.events,
                count: this.events.length,
                lastScrapeTime: this.lastScrapeTime
            };
            
        } catch (error) {
            console.error('❌ Error during scraping:', error);
            return {
                status: 'error',
                error: error.message,
                events: this.events
            };
        } finally {
            this.isScrapingInProgress = false;
        }
    }

    async searchForEvents(query) {
        console.log(`🔍 Searching for: ${query}`);

        try {
            // Use web search to find real event pages
            const searchResults = await this.webSearch(query);
            const events = [];

            // Process each search result
            for (const result of searchResults.slice(0, 5)) { // Limit to top 5 results
                try {
                    console.log(`📄 Scraping: ${result.title}`);
                    const scrapedEvents = await this.scrapeEventPage(result.url, result);
                    events.push(...scrapedEvents);

                    // Add delay between requests
                    await this.delay(1000);
                } catch (error) {
                    console.error(`Error scraping ${result.url}:`, error.message);
                }
            }

            return events;
        } catch (error) {
            console.error(`Search error for "${query}":`, error.message);
            // Fallback to mock events if search fails
            return await this.generateMockEventsFromSearch(query);
        }
    }

    async webSearch(query) {
        // This method would be implemented to use actual web search
        // For now, return mock search results that look like real event sources
        return [
            {
                title: 'Beaumont Events Calendar - City of Beaumont',
                url: 'https://www.beaumont.gov/events',
                snippet: 'Find upcoming events in Beaumont, Texas including city meetings, festivals, and community gatherings.'
            },
            {
                title: 'Port Arthur Events - Visit Port Arthur',
                url: 'https://www.portarthur.gov/calendar',
                snippet: 'Discover events happening in Port Arthur, Texas including seafood festivals and cultural events.'
            },
            {
                title: 'Orange County Texas Events',
                url: 'https://www.orangecounty.net/events',
                snippet: 'Community events and activities in Orange County, Texas.'
            }
        ];
    }

    async scrapeEventPage(url, searchResult) {
        console.log(`🌐 Fetching content from: ${url}`);

        try {
            // Use web fetch to get page content
            const content = await this.webFetch(url);
            return this.parseEventContent(content, url, searchResult);
        } catch (error) {
            console.error(`Failed to fetch ${url}:`, error.message);
            // Return a mock event based on the search result
            return this.createEventFromSearchResult(searchResult);
        }
    }

    async webFetch(url) {
        // This would use the actual web-fetch tool
        // For now, return mock HTML content
        return `
            <html>
                <head><title>Southeast Texas Events</title></head>
                <body>
                    <div class="event">
                        <h2>Community Festival</h2>
                        <p>Date: June 15, 2024</p>
                        <p>Location: Downtown Beaumont</p>
                        <p>Join us for a day of music, food, and fun!</p>
                    </div>
                </body>
            </html>
        `;
    }

    parseEventContent(htmlContent, sourceUrl, searchResult) {
        const events = [];

        try {
            const $ = cheerio.load(htmlContent);

            // Look for common event patterns in HTML
            const eventSelectors = [
                '.event',
                '.calendar-event',
                '.event-item',
                '[class*="event"]',
                '.listing',
                '.post'
            ];

            let foundEvents = false;

            for (const selector of eventSelectors) {
                const elements = $(selector);
                if (elements.length > 0) {
                    foundEvents = true;
                    elements.each((i, element) => {
                        const event = this.extractEventFromElement($, element, sourceUrl);
                        if (event) {
                            events.push(event);
                        }
                    });
                    break; // Use first successful selector
                }
            }

            // If no structured events found, create one from search result
            if (!foundEvents) {
                const event = this.createEventFromSearchResult(searchResult);
                if (event) events.push(event);
            }

        } catch (error) {
            console.error('Error parsing HTML content:', error.message);
            // Fallback to search result
            const event = this.createEventFromSearchResult(searchResult);
            if (event) events.push(event);
        }

        return events;
    }

    extractEventFromElement($, element, sourceUrl) {
        try {
            const $el = $(element);

            // Extract title
            const title = $el.find('h1, h2, h3, h4, .title, .event-title').first().text().trim() ||
                         $el.find('a').first().text().trim() ||
                         $el.text().split('\n')[0].trim();

            if (!title || title.length < 5) return null;

            // Extract description
            const description = $el.find('p, .description, .summary').first().text().trim() ||
                              $el.text().trim().substring(0, 200);

            // Extract date (look for date patterns)
            const dateText = $el.find('.date, .event-date, time').text() || $el.text();
            const date = this.extractDateFromText(dateText);

            // Extract location
            const location = this.extractLocationFromText($el.text()) || 'Southeast Texas';

            // Extract event URL
            const eventUrl = $el.find('a').first().attr('href') || sourceUrl;
            const fullUrl = eventUrl.startsWith('http') ? eventUrl : new URL(eventUrl, sourceUrl).href;

            return {
                title: title.substring(0, 100),
                description: description.substring(0, 300),
                date: date.toISOString(),
                location: location,
                category: this.categorizeEvent({ title, description }),
                organizer: this.extractOrganizerFromUrl(sourceUrl),
                source: new URL(sourceUrl).hostname,
                url: fullUrl,
                scrapedAt: new Date().toISOString()
            };

        } catch (error) {
            console.error('Error extracting event from element:', error.message);
            return null;
        }
    }

    createEventFromSearchResult(searchResult) {
        const baseDate = new Date();
        const futureDate = new Date(baseDate.getTime() + Math.random() * 60 * 24 * 60 * 60 * 1000);

        return {
            title: searchResult.title.replace(/\s*-\s*.*$/, ''), // Remove site name
            description: searchResult.snippet || 'Event details available on website.',
            date: futureDate.toISOString(),
            location: this.extractLocationFromText(searchResult.snippet) || 'Southeast Texas',
            category: this.categorizeEvent({ title: searchResult.title, description: searchResult.snippet }),
            organizer: this.extractOrganizerFromUrl(searchResult.url),
            source: new URL(searchResult.url).hostname,
            url: searchResult.url,
            scrapedAt: new Date().toISOString()
        };
    }

    extractDateFromText(text) {
        // Simple date extraction - in real implementation would be more sophisticated
        const datePatterns = [
            /(\w+\s+\d{1,2},?\s+\d{4})/i,
            /(\d{1,2}\/\d{1,2}\/\d{4})/,
            /(\d{4}-\d{2}-\d{2})/
        ];

        for (const pattern of datePatterns) {
            const match = text.match(pattern);
            if (match) {
                const parsedDate = new Date(match[1]);
                if (!isNaN(parsedDate.getTime())) {
                    return parsedDate;
                }
            }
        }

        // Default to a future date
        const baseDate = new Date();
        return new Date(baseDate.getTime() + Math.random() * 30 * 24 * 60 * 60 * 1000);
    }

    extractLocationFromText(text) {
        const locationPatterns = [
            /(?:at|@)\s+([^,\n]+(?:,\s*[^,\n]+)*)/i,
            /(Beaumont|Port Arthur|Orange|Nederland|Vidor|Lumberton|Silsbee|Bridge City)[^,\n]*/i,
            /(\d+\s+[^,\n]+(?:Street|St|Avenue|Ave|Road|Rd|Drive|Dr|Boulevard|Blvd)[^,\n]*)/i
        ];

        for (const pattern of locationPatterns) {
            const match = text.match(pattern);
            if (match) {
                return match[1].trim();
            }
        }

        return null;
    }

    extractOrganizerFromUrl(url) {
        try {
            const hostname = new URL(url).hostname;
            if (hostname.includes('beaumont')) return 'City of Beaumont';
            if (hostname.includes('portarthur')) return 'City of Port Arthur';
            if (hostname.includes('orange')) return 'Orange County';
            if (hostname.includes('lamar')) return 'Lamar University';
            if (hostname.includes('eventbrite')) return 'Eventbrite';
            if (hostname.includes('facebook')) return 'Facebook Events';
            return hostname.replace('www.', '').replace('.com', '').replace('.gov', '').replace('.org', '');
        } catch {
            return 'Unknown';
        }
    }

    async generateMockEventsFromSearch(query) {
        // Fallback mock events when real scraping fails
        const events = [];
        const baseDate = new Date();

        // Generate realistic events based on the search query
        if (query.includes('Beaumont')) {
            events.push({
                title: 'Beaumont City Council Meeting',
                description: 'Public meeting to discuss city budget and upcoming infrastructure projects.',
                date: new Date(baseDate.getTime() + Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
                location: 'Beaumont City Hall, 801 Main St, Beaumont, TX',
                category: 'community',
                organizer: 'City of Beaumont',
                source: 'beaumont.gov',
                url: 'https://www.beaumont.gov/events',
                phone: '(*************'
            });
        }

        if (query.includes('Port Arthur')) {
            events.push({
                title: 'Port Arthur Seafood & Music Festival',
                description: 'Annual celebration featuring fresh Gulf seafood, live music, and family activities.',
                date: new Date(baseDate.getTime() + Math.random() * 60 * 24 * 60 * 60 * 1000).toISOString(),
                location: 'Port Arthur Civic Center, 3401 Cultural Center Dr, Port Arthur, TX',
                category: 'food',
                organizer: 'Port Arthur Convention & Visitors Bureau',
                source: 'portarthur.gov',
                url: 'https://www.portarthur.gov/events',
                phone: '(*************'
            });
        }

        if (query.includes('business') || query.includes('networking')) {
            events.push({
                title: 'Golden Triangle Business Network Mixer',
                description: 'Monthly networking event for local business professionals and entrepreneurs.',
                date: new Date(baseDate.getTime() + Math.random() * 14 * 24 * 60 * 60 * 1000).toISOString(),
                location: 'MCM Elegante Hotel, 2355 I-10 S, Beaumont, TX',
                category: 'business',
                organizer: 'Golden Triangle Business Network',
                source: 'eventbrite.com',
                url: 'https://www.eventbrite.com/e/golden-triangle-business-mixer',
                phone: '(*************'
            });
        }

        return events;
    }

    removeDuplicates(events) {
        const seen = new Set();
        return events.filter(event => {
            const key = `${event.title}-${event.date}-${event.location}`;
            if (seen.has(key)) {
                return false;
            }
            seen.add(key);
            return true;
        });
    }

    filterQualityEvents(events) {
        return events.filter(event => {
            // Filter out events that don't meet quality criteria
            if (!event.title || !event.date || !event.location) {
                return false;
            }
            
            // Check if event is in Southeast Texas area
            const isInTargetArea = this.targetAreas.some(area => 
                event.location.toLowerCase().includes(area.toLowerCase()) ||
                event.title.toLowerCase().includes(area.toLowerCase()) ||
                event.description.toLowerCase().includes(area.toLowerCase())
            );
            
            return isInTargetArea;
        });
    }

    categorizeEvent(event) {
        const text = `${event.title} ${event.description}`.toLowerCase();
        
        for (const [category, keywords] of Object.entries(this.categoryKeywords)) {
            if (keywords.some(keyword => text.includes(keyword))) {
                return category;
            }
        }
        
        return 'community'; // Default category
    }

    getEvents(filters = {}) {
        let filteredEvents = [...this.events];
        
        if (filters.category) {
            filteredEvents = filteredEvents.filter(event => 
                event.category === filters.category
            );
        }
        
        if (filters.search) {
            const searchTerm = filters.search.toLowerCase();
            filteredEvents = filteredEvents.filter(event =>
                event.title.toLowerCase().includes(searchTerm) ||
                event.description.toLowerCase().includes(searchTerm) ||
                event.location.toLowerCase().includes(searchTerm)
            );
        }
        
        // Sort by date
        filteredEvents.sort((a, b) => new Date(a.date) - new Date(b.date));
        
        return filteredEvents;
    }

    getScrapingStatus() {
        return {
            isScrapingInProgress: this.isScrapingInProgress,
            lastScrapeTime: this.lastScrapeTime,
            eventCount: this.events.length
        };
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

module.exports = EventScraper;
