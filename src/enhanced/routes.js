/**
 * Enhanced Event Routes
 * API endpoints for the enhanced event management system
 */

const express = require('express');
const router = express.Router();
const PriorityVenuesDatabase = require('./priorityVenues');
const VenueSpecificScraper = require('./venueSpecificScraper');

// GET /api/enhanced/events - Get all enhanced events
router.get('/events', async (req, res) => {
    try {
        const eventEnhancer = req.app.locals.eventEnhancer;
        const { category, search, verified, venue } = req.query;
        
        let events = eventEnhancer.getAllEnhancedEvents();
        
        // Apply filters
        if (category) {
            events = events.filter(event => 
                event.originalEvent.category === category ||
                event.extractedTags.includes(category)
            );
        }
        
        if (search) {
            const searchTerm = search.toLowerCase();
            events = events.filter(event =>
                event.originalEvent.title.toLowerCase().includes(searchTerm) ||
                event.originalEvent.description.toLowerCase().includes(searchTerm) ||
                event.extractedVenueName.toLowerCase().includes(searchTerm) ||
                event.extractedTags.some(tag => tag.includes(searchTerm))
            );
        }
        
        if (verified !== undefined) {
            events = events.filter(event => event.isVerified === (verified === 'true'));
        }
        
        if (venue) {
            events = events.filter(event => 
                event.venue && event.venue.id === venue
            );
        }
        
        // Sort by priority, then date
        events.sort((a, b) => {
            if (a.priority !== b.priority) {
                return b.priority - a.priority;
            }
            return new Date(a.originalEvent.date) - new Date(b.originalEvent.date);
        });
        
        res.json({
            success: true,
            events,
            count: events.length
        });
    } catch (error) {
        console.error('Error fetching enhanced events:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch enhanced events',
            message: error.message
        });
    }
});

// GET /api/enhanced/events/:id - Get specific enhanced event
router.get('/events/:id', async (req, res) => {
    try {
        const eventEnhancer = req.app.locals.eventEnhancer;
        const event = eventEnhancer.getEnhancedEvent(req.params.id);
        
        if (!event) {
            return res.status(404).json({
                success: false,
                error: 'Event not found'
            });
        }
        
        res.json({
            success: true,
            event
        });
    } catch (error) {
        console.error('Error fetching enhanced event:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch enhanced event',
            message: error.message
        });
    }
});

// PUT /api/enhanced/events/:id - Update enhanced event
router.put('/events/:id', async (req, res) => {
    try {
        const eventEnhancer = req.app.locals.eventEnhancer;
        const updates = req.body;
        
        const updatedEvent = await eventEnhancer.updateEnhancedEvent(req.params.id, updates);
        
        res.json({
            success: true,
            event: updatedEvent
        });
    } catch (error) {
        console.error('Error updating enhanced event:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to update enhanced event',
            message: error.message
        });
    }
});

// POST /api/enhanced/enhance - Enhance events from original scraper
router.post('/enhance', async (req, res) => {
    try {
        const eventEnhancer = req.app.locals.eventEnhancer;
        const originalScraper = req.app.locals.eventScraper;
        
        // Get original events
        const originalEvents = originalScraper.getEvents();
        
        // Enhance them
        const enhancedEvents = await eventEnhancer.enhanceEventsFromOriginal(originalEvents);
        
        res.json({
            success: true,
            message: `Enhanced ${enhancedEvents.length} events`,
            count: enhancedEvents.length,
            events: enhancedEvents
        });
    } catch (error) {
        console.error('Error enhancing events:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to enhance events',
            message: error.message
        });
    }
});

// GET /api/enhanced/venues - Get all venues
router.get('/venues', async (req, res) => {
    try {
        const eventEnhancer = req.app.locals.eventEnhancer;
        const { search, verified } = req.query;
        
        let venues = eventEnhancer.getAllVenues();
        
        // Apply filters
        if (search) {
            const searchTerm = search.toLowerCase();
            venues = venues.filter(venue =>
                venue.name.toLowerCase().includes(searchTerm) ||
                venue.address.toLowerCase().includes(searchTerm)
            );
        }
        
        if (verified !== undefined) {
            venues = venues.filter(venue => venue.isVerified === (verified === 'true'));
        }
        
        // Sort by name
        venues.sort((a, b) => a.name.localeCompare(b.name));
        
        res.json({
            success: true,
            venues,
            count: venues.length
        });
    } catch (error) {
        console.error('Error fetching venues:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch venues',
            message: error.message
        });
    }
});

// GET /api/enhanced/venues/:id - Get specific venue
router.get('/venues/:id', async (req, res) => {
    try {
        const eventEnhancer = req.app.locals.eventEnhancer;
        const priorityVenues = new PriorityVenuesDatabase();

        // First try enhanced venues
        let venue = eventEnhancer.getVenue(req.params.id);

        // If not found, try priority venues database
        if (!venue) {
            venue = priorityVenues.getVenueById(req.params.id);
        }

        if (!venue) {
            return res.status(404).json({
                success: false,
                error: 'Venue not found'
            });
        }

        res.json({
            success: true,
            venue
        });
    } catch (error) {
        console.error('Error fetching venue:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch venue',
            message: error.message
        });
    }
});

// PUT /api/enhanced/venues/:id - Update venue
router.put('/venues/:id', async (req, res) => {
    try {
        const eventEnhancer = req.app.locals.eventEnhancer;
        const updates = req.body;
        
        const updatedVenue = await eventEnhancer.updateVenue(req.params.id, updates);
        
        res.json({
            success: true,
            venue: updatedVenue
        });
    } catch (error) {
        console.error('Error updating venue:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to update venue',
            message: error.message
        });
    }
});

// GET /api/enhanced/stats - Get system statistics
router.get('/stats', async (req, res) => {
    try {
        const eventEnhancer = req.app.locals.eventEnhancer;
        const originalScraper = req.app.locals.eventScraper;
        
        const enhancedEvents = eventEnhancer.getAllEnhancedEvents();
        const venues = eventEnhancer.getAllVenues();
        const originalEvents = originalScraper.getEvents();
        
        const stats = {
            originalEvents: originalEvents.length,
            enhancedEvents: enhancedEvents.length,
            verifiedEvents: enhancedEvents.filter(e => e.isVerified).length,
            totalVenues: venues.length,
            verifiedVenues: venues.filter(v => v.isVerified).length,
            categoryCounts: {},
            tagCounts: {},
            recentActivity: {
                lastEnhancement: enhancedEvents.length > 0 ? 
                    Math.max(...enhancedEvents.map(e => new Date(e.lastEnhanced))) : null,
                lastOriginalScrape: originalScraper.lastScrapeTime
            }
        };
        
        // Count categories
        enhancedEvents.forEach(event => {
            const category = event.originalEvent.category;
            stats.categoryCounts[category] = (stats.categoryCounts[category] || 0) + 1;
        });
        
        // Count tags
        enhancedEvents.forEach(event => {
            event.extractedTags.forEach(tag => {
                stats.tagCounts[tag] = (stats.tagCounts[tag] || 0) + 1;
            });
        });
        
        res.json({
            success: true,
            stats
        });
    } catch (error) {
        console.error('Error fetching stats:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch stats',
            message: error.message
        });
    }
});

// GET /api/enhanced/priority-venues - Get priority venues database
router.get('/priority-venues', async (req, res) => {
    try {
        const priorityVenues = new PriorityVenuesDatabase();
        const { city, type, category, search, minPriority } = req.query;

        let venues = priorityVenues.getPriorityVenues();

        // Apply filters
        if (city) {
            venues = priorityVenues.getVenuesByCity(city);
        }

        if (type) {
            venues = priorityVenues.getVenuesByType(type);
        }

        if (category) {
            venues = priorityVenues.getVenuesByCategory(category);
        }

        if (search) {
            venues = priorityVenues.searchVenues(search);
        }

        if (minPriority) {
            venues = priorityVenues.getHighPriorityVenues(parseInt(minPriority));
        }

        res.json({
            success: true,
            venues,
            count: venues.length
        });
    } catch (error) {
        console.error('Error fetching priority venues:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch priority venues',
            message: error.message
        });
    }
});

// GET /api/enhanced/priority-venues/:id - Get specific priority venue
router.get('/priority-venues/:id', async (req, res) => {
    try {
        const priorityVenues = new PriorityVenuesDatabase();
        const venue = priorityVenues.getVenueById(req.params.id);

        if (!venue) {
            return res.status(404).json({
                success: false,
                error: 'Priority venue not found'
            });
        }

        res.json({
            success: true,
            venue
        });
    } catch (error) {
        console.error('Error fetching priority venue:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch priority venue',
            message: error.message
        });
    }
});

// POST /api/enhanced/scrape-venues - Scrape priority venues
router.post('/scrape-venues', async (req, res) => {
    try {
        const webScrapingService = req.app.locals.eventScraper;
        const venueSpecificScraper = new VenueSpecificScraper(webScrapingService);

        // Start venue-specific scraping
        const events = await venueSpecificScraper.scrapeAllPriorityVenues();

        // Enhance the scraped events
        const eventEnhancer = req.app.locals.eventEnhancer;
        const enhancedEvents = await eventEnhancer.enhanceEventsFromOriginal(events);

        // Get venue stats
        const stats = venueSpecificScraper.getVenueStats();

        res.json({
            success: true,
            message: `Scraped ${events.length} events from priority venues`,
            events: enhancedEvents,
            stats,
            count: events.length
        });
    } catch (error) {
        console.error('Error scraping priority venues:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to scrape priority venues',
            message: error.message
        });
    }
});

// GET /api/enhanced/venue-events/:venueId - Get events for specific venue
router.get('/venue-events/:venueId', async (req, res) => {
    try {
        const eventEnhancer = req.app.locals.eventEnhancer;
        const { category, search, verified } = req.query;

        let events = eventEnhancer.getAllEnhancedEvents();

        // Filter by venue
        events = events.filter(event =>
            event.venue && event.venue.id === req.params.venueId ||
            event.venueId === req.params.venueId
        );

        // Apply additional filters
        if (category) {
            events = events.filter(event =>
                event.originalEvent.category === category ||
                event.extractedTags.includes(category)
            );
        }

        if (search) {
            const searchTerm = search.toLowerCase();
            events = events.filter(event =>
                event.originalEvent.title.toLowerCase().includes(searchTerm) ||
                event.originalEvent.description.toLowerCase().includes(searchTerm)
            );
        }

        if (verified !== undefined) {
            events = events.filter(event => event.isVerified === (verified === 'true'));
        }

        // Sort by date
        events.sort((a, b) => new Date(a.originalEvent.date) - new Date(b.originalEvent.date));

        res.json({
            success: true,
            events,
            count: events.length,
            venueId: req.params.venueId
        });
    } catch (error) {
        console.error('Error fetching venue events:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch venue events',
            message: error.message
        });
    }
});

module.exports = router;

// Add fallback routes for venue lookup
const express2 = require('express');
const fallbackRouter = express2.Router();

// GET /api/venues/:id - Fallback venue lookup
fallbackRouter.get('/venues/:id', async (req, res) => {
    try {
        const priorityVenues = new PriorityVenuesDatabase();
        const venue = priorityVenues.getVenueById(req.params.id);

        if (!venue) {
            return res.status(404).json({
                success: false,
                error: 'Venue not found'
            });
        }

        res.json({
            success: true,
            venue
        });
    } catch (error) {
        console.error('Error fetching venue:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch venue',
            message: error.message
        });
    }
});

module.exports.fallbackRouter = fallbackRouter;
