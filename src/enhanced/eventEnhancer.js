/**
 * Enhanced Event Management System
 * Extracts additional data from events and creates detailed event records
 * Works alongside the existing system without modifying it
 */

const fs = require('fs').promises;
const path = require('path');

class EventEnhancer {
    constructor() {
        this.enhancedEvents = new Map(); // eventId -> enhanced event data
        this.venues = new Map(); // venueId -> venue data
        this.dataDir = path.join(__dirname, '../../data');
        this.eventsFile = path.join(this.dataDir, 'enhanced-events.json');
        this.venuesFile = path.join(this.dataDir, 'venues.json');
        
        this.init();
    }

    async init() {
        try {
            // Ensure data directory exists
            await fs.mkdir(this.dataDir, { recursive: true });
            
            // Load existing data
            await this.loadData();
            
            console.log('🎯 EventEnhancer initialized');
            console.log(`📊 Loaded ${this.enhancedEvents.size} enhanced events`);
            console.log(`🏢 Loaded ${this.venues.size} venues`);
        } catch (error) {
            console.error('❌ Error initializing EventEnhancer:', error);
        }
    }

    async loadData() {
        try {
            // Load enhanced events
            try {
                const eventsData = await fs.readFile(this.eventsFile, 'utf8');
                const events = JSON.parse(eventsData);
                this.enhancedEvents = new Map(Object.entries(events));
            } catch (error) {
                console.log('📝 No existing enhanced events file, starting fresh');
            }

            // Load venues
            try {
                const venuesData = await fs.readFile(this.venuesFile, 'utf8');
                const venues = JSON.parse(venuesData);
                this.venues = new Map(Object.entries(venues));
            } catch (error) {
                console.log('📝 No existing venues file, starting fresh');
            }
        } catch (error) {
            console.error('❌ Error loading data:', error);
        }
    }

    async saveData() {
        try {
            // Save enhanced events
            const eventsObj = Object.fromEntries(this.enhancedEvents);
            await fs.writeFile(this.eventsFile, JSON.stringify(eventsObj, null, 2));

            // Save venues
            const venuesObj = Object.fromEntries(this.venues);
            await fs.writeFile(this.venuesFile, JSON.stringify(venuesObj, null, 2));

            console.log('💾 Data saved successfully');
        } catch (error) {
            console.error('❌ Error saving data:', error);
        }
    }

    generateEventId(event) {
        // Create a unique ID based on event properties
        const key = `${event.title}-${event.date}-${event.location}`;
        return Buffer.from(key).toString('base64').replace(/[^a-zA-Z0-9]/g, '').substring(0, 16);
    }

    generateVenueId(venueName, address) {
        const key = `${venueName}-${address}`;
        return Buffer.from(key).toString('base64').replace(/[^a-zA-Z0-9]/g, '').substring(0, 12);
    }

    async enhanceEvent(originalEvent) {
        const eventId = this.generateEventId(originalEvent);
        
        // Check if already enhanced
        if (this.enhancedEvents.has(eventId)) {
            return this.enhancedEvents.get(eventId);
        }

        console.log(`🔍 Enhancing event: ${originalEvent.title}`);

        // Extract venue information
        const venueInfo = this.extractVenueInfo(originalEvent);
        
        // Create or update venue
        let venue = null;
        if (venueInfo.name && venueInfo.address) {
            venue = await this.createOrUpdateVenue(venueInfo);
        }

        // Create enhanced event
        const enhancedEvent = {
            id: eventId,
            originalEvent: originalEvent,
            
            // Enhanced fields
            venue: venue,
            extractedAddress: venueInfo.address,
            extractedVenueName: venueInfo.name,
            extractedPhone: this.extractPhone(originalEvent),
            extractedEmail: this.extractEmail(originalEvent),
            extractedPrice: this.extractPrice(originalEvent),
            extractedTags: this.extractTags(originalEvent),
            
            // Admin fields
            isVerified: false,
            adminNotes: '',
            customDescription: '',
            featuredImage: '',
            priority: 0,
            
            // Metadata
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            lastEnhanced: new Date().toISOString()
        };

        // Store enhanced event
        this.enhancedEvents.set(eventId, enhancedEvent);
        await this.saveData();

        return enhancedEvent;
    }

    extractVenueInfo(event) {
        const location = event.location || '';
        const description = event.description || '';
        const title = event.title || '';
        
        // Try to extract venue name and address
        let venueName = '';
        let address = '';

        // Common venue patterns
        const venuePatterns = [
            /at\s+([^,]+),?\s*(.+)/i,
            /venue:\s*([^,]+),?\s*(.+)/i,
            /location:\s*([^,]+),?\s*(.+)/i,
            /held at\s+([^,]+),?\s*(.+)/i
        ];

        for (const pattern of venuePatterns) {
            const match = location.match(pattern);
            if (match) {
                venueName = match[1].trim();
                address = match[2].trim();
                break;
            }
        }

        // If no pattern match, try to split by comma
        if (!venueName && location.includes(',')) {
            const parts = location.split(',');
            if (parts.length >= 2) {
                venueName = parts[0].trim();
                address = parts.slice(1).join(',').trim();
            }
        }

        // Fallback: use entire location as venue name
        if (!venueName) {
            venueName = location;
        }

        return {
            name: venueName,
            address: address || location
        };
    }

    async createOrUpdateVenue(venueInfo) {
        const venueId = this.generateVenueId(venueInfo.name, venueInfo.address);
        
        if (this.venues.has(venueId)) {
            return this.venues.get(venueId);
        }

        const venue = {
            id: venueId,
            name: venueInfo.name,
            address: venueInfo.address,
            
            // Enhanced venue fields
            phone: '',
            email: '',
            website: '',
            capacity: null,
            type: '', // theater, park, center, etc.
            amenities: [],
            parking: '',
            accessibility: '',
            
            // Coordinates (to be filled later)
            latitude: null,
            longitude: null,
            
            // Admin fields
            isVerified: false,
            adminNotes: '',
            
            // Metadata
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            eventCount: 1
        };

        this.venues.set(venueId, venue);
        await this.saveData();

        console.log(`🏢 Created venue: ${venue.name}`);
        return venue;
    }

    extractPhone(event) {
        const text = `${event.description} ${event.title}`;
        const phonePattern = /(\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4})/g;
        const matches = text.match(phonePattern);
        return matches ? matches[0] : '';
    }

    extractEmail(event) {
        const text = `${event.description} ${event.title}`;
        const emailPattern = /([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/g;
        const matches = text.match(emailPattern);
        return matches ? matches[0] : '';
    }

    extractPrice(event) {
        const text = `${event.description} ${event.title}`;
        const pricePatterns = [
            /\$(\d+(?:\.\d{2})?)/g,
            /free/i,
            /no charge/i,
            /admission free/i
        ];
        
        for (const pattern of pricePatterns) {
            const matches = text.match(pattern);
            if (matches) {
                return matches[0];
            }
        }
        return '';
    }

    extractTags(event) {
        const text = `${event.description} ${event.title} ${event.category}`.toLowerCase();
        const tags = [];
        
        const tagKeywords = {
            'music': ['concert', 'music', 'band', 'singer', 'performance'],
            'food': ['food', 'dining', 'restaurant', 'cuisine', 'meal'],
            'family': ['family', 'kids', 'children', 'child-friendly'],
            'outdoor': ['outdoor', 'park', 'nature', 'hiking', 'camping'],
            'business': ['networking', 'business', 'professional', 'career'],
            'education': ['workshop', 'seminar', 'training', 'class', 'learn'],
            'arts': ['art', 'gallery', 'exhibition', 'craft', 'creative'],
            'sports': ['sports', 'game', 'tournament', 'athletic', 'fitness'],
            'community': ['community', 'volunteer', 'charity', 'fundraiser']
        };

        for (const [tag, keywords] of Object.entries(tagKeywords)) {
            if (keywords.some(keyword => text.includes(keyword))) {
                tags.push(tag);
            }
        }

        return tags;
    }

    async enhanceEventsFromOriginal(originalEvents) {
        console.log(`🎯 Enhancing ${originalEvents.length} events...`);
        const enhanced = [];
        
        for (const event of originalEvents) {
            try {
                const enhancedEvent = await this.enhanceEvent(event);
                enhanced.push(enhancedEvent);
            } catch (error) {
                console.error(`❌ Error enhancing event ${event.title}:`, error);
            }
        }
        
        console.log(`✅ Enhanced ${enhanced.length} events`);
        return enhanced;
    }

    getEnhancedEvent(eventId) {
        return this.enhancedEvents.get(eventId);
    }

    getAllEnhancedEvents() {
        return Array.from(this.enhancedEvents.values());
    }

    getVenue(venueId) {
        return this.venues.get(venueId);
    }

    getAllVenues() {
        return Array.from(this.venues.values());
    }

    async updateEnhancedEvent(eventId, updates) {
        const event = this.enhancedEvents.get(eventId);
        if (!event) {
            throw new Error(`Event ${eventId} not found`);
        }

        const updatedEvent = {
            ...event,
            ...updates,
            updatedAt: new Date().toISOString()
        };

        this.enhancedEvents.set(eventId, updatedEvent);
        await this.saveData();

        return updatedEvent;
    }

    async updateVenue(venueId, updates) {
        const venue = this.venues.get(venueId);
        if (!venue) {
            throw new Error(`Venue ${venueId} not found`);
        }

        const updatedVenue = {
            ...venue,
            ...updates,
            updatedAt: new Date().toISOString()
        };

        this.venues.set(venueId, updatedVenue);
        await this.saveData();

        return updatedVenue;
    }
}

module.exports = EventEnhancer;
