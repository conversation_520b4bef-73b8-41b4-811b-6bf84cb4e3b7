const cheerio = require('cheerio');

class RealEventAgent {
    constructor() {
        this.events = [];
        this.isScrapingInProgress = false;
        this.lastScrapeTime = null;
        
        // Southeast Texas cities and areas to focus on
        this.targetAreas = [
            'Beaumont TX',
            'Port Arthur TX', 
            'Orange TX',
            'Nederland TX',
            'Vidor TX',
            'Lumberton TX',
            'Silsbee TX',
            'Bridge City TX',
            'Southeast Texas',
            'Golden Triangle Texas'
        ];
        
        // Event categories mapping
        this.categoryKeywords = {
            business: ['business', 'networking', 'conference', 'seminar', 'workshop', 'job fair', 'career'],
            entertainment: ['concert', 'music', 'festival', 'show', 'performance', 'comedy', 'theater'],
            education: ['class', 'workshop', 'seminar', 'lecture', 'training', 'course', 'school'],
            sports: ['game', 'tournament', 'race', 'sports', 'athletic', 'fitness', 'run', 'walk'],
            community: ['community', 'volunteer', 'charity', 'fundraiser', 'meeting', 'cleanup'],
            arts: ['art', 'gallery', 'museum', 'craft', 'painting', 'sculpture', 'exhibition'],
            food: ['food', 'restaurant', 'dining', 'cooking', 'festival', 'taste', 'culinary'],
            health: ['health', 'wellness', 'medical', 'fitness', 'yoga', 'meditation', 'screening']
        };
    }

    async scrapeRealEvents(webSearchTool, webFetchTool) {
        if (this.isScrapingInProgress) {
            console.log('Scraping already in progress...');
            return { status: 'already_running', events: this.events };
        }

        this.isScrapingInProgress = true;
        console.log('🔍 Starting REAL web scraping for Southeast Texas events...');
        
        try {
            const newEvents = [];
            
            // Search queries targeting Southeast Texas events
            const searchQueries = [
                'events Beaumont Texas 2024 2025 calendar',
                'Port Arthur Texas events festivals concerts',
                'Orange Texas community events calendar',
                'Southeast Texas Golden Triangle events',
                'Lamar University Beaumont events calendar',
                'Jefferson County Texas events meetings',
                'Beaumont civic center events schedule',
                'Southeast Texas business networking events'
            ];

            for (const query of searchQueries) {
                console.log(`🔍 Real web search: ${query}`);
                try {
                    // Use the actual web search tool
                    const searchResults = await webSearchTool({
                        query: query,
                        num_results: 6
                    });
                    
                    console.log(`📊 Found ${searchResults.length} real search results`);
                    
                    // Process each search result
                    for (const result of searchResults) {
                        try {
                            if (this.isEventRelevantUrl(result.url, result.title)) {
                                console.log(`📄 Scraping: ${result.title}`);
                                console.log(`🔗 URL: ${result.url}`);
                                
                                // Use the actual web fetch tool
                                const htmlContent = await webFetchTool({
                                    url: result.url
                                });
                                
                                console.log(`📝 Received ${htmlContent.length} characters of real content`);
                                
                                const pageEvents = this.parseEventContent(htmlContent, result);
                                newEvents.push(...pageEvents);
                                
                                // Add delay between requests
                                await this.delay(2000);
                            } else {
                                console.log(`⏭️  Skipping non-event URL: ${result.url}`);
                            }
                        } catch (error) {
                            console.error(`Error scraping ${result.url}:`, error.message);
                            
                            // Create fallback event from search result
                            const fallbackEvent = this.createEventFromSearchResult(result);
                            if (fallbackEvent && this.isValidEvent(fallbackEvent)) {
                                newEvents.push(fallbackEvent);
                            }
                        }
                    }
                    
                    // Add delay between searches
                    await this.delay(3000);
                    
                } catch (error) {
                    console.error(`Error searching for "${query}":`, error.message);
                }
            }

            // Remove duplicates and filter for quality
            const uniqueEvents = this.removeDuplicates(newEvents);
            const filteredEvents = this.filterQualityEvents(uniqueEvents);
            
            this.events = filteredEvents;
            this.lastScrapeTime = new Date();
            
            console.log(`✅ Real scraping completed! Found ${this.events.length} unique events`);
            
            return {
                status: 'success',
                events: this.events,
                count: this.events.length,
                lastScrapeTime: this.lastScrapeTime
            };
            
        } catch (error) {
            console.error('❌ Error during real scraping:', error);
            return {
                status: 'error',
                error: error.message,
                events: this.events
            };
        } finally {
            this.isScrapingInProgress = false;
        }
    }

    isEventRelevantUrl(url, title) {
        const eventKeywords = [
            'event', 'calendar', 'festival', 'concert', 'meeting', 'conference',
            'workshop', 'seminar', 'show', 'performance', 'gathering', 'celebration'
        ];
        
        const locationKeywords = [
            'beaumont', 'port arthur', 'orange', 'southeast texas', 'golden triangle',
            'jefferson county', 'lamar university'
        ];
        
        const urlLower = url.toLowerCase();
        const titleLower = title.toLowerCase();
        const combined = urlLower + ' ' + titleLower;
        
        const hasEventKeyword = eventKeywords.some(keyword => combined.includes(keyword));
        const hasLocationKeyword = locationKeywords.some(keyword => combined.includes(keyword));
        
        // Skip social media posts, news articles, and irrelevant sites
        const skipPatterns = [
            'facebook.com/posts', 'twitter.com', 'instagram.com',
            'news', 'article', 'blog', 'wikipedia', 'yelp'
        ];
        
        const shouldSkip = skipPatterns.some(pattern => urlLower.includes(pattern));
        
        return hasEventKeyword && hasLocationKeyword && !shouldSkip;
    }

    parseEventContent(htmlContent, searchResult) {
        const events = [];
        
        try {
            const $ = cheerio.load(htmlContent);
            
            // Remove script and style tags
            $('script, style, nav, footer, header').remove();
            
            // Look for event-specific patterns
            const eventSelectors = [
                '.event', '.calendar-event', '.event-item', '.event-listing',
                '[class*="event"]', '.listing', '.post', '.entry',
                '.calendar-item', '.schedule-item', 'article'
            ];
            
            let foundEvents = false;
            
            for (const selector of eventSelectors) {
                const elements = $(selector);
                if (elements.length > 0) {
                    console.log(`📋 Found ${elements.length} potential events with selector: ${selector}`);
                    foundEvents = true;
                    
                    elements.each((i, element) => {
                        if (i < 10) { // Limit to first 10 events per page
                            const event = this.extractEventFromElement($, element, searchResult);
                            if (event && this.isValidEvent(event)) {
                                events.push(event);
                            }
                        }
                    });
                    
                    if (events.length > 0) break; // Use first successful selector
                }
            }
            
            // If no structured events found, try to extract from page content
            if (!foundEvents || events.length === 0) {
                console.log('📄 No structured events found, creating from search result');
                const event = this.createEventFromSearchResult(searchResult);
                if (event && this.isValidEvent(event)) {
                    events.push(event);
                }
            }
            
        } catch (error) {
            console.error('Error parsing HTML content:', error.message);
            // Fallback to search result
            const event = this.createEventFromSearchResult(searchResult);
            if (event && this.isValidEvent(event)) {
                events.push(event);
            }
        }
        
        console.log(`✅ Extracted ${events.length} valid events from page`);
        return events;
    }

    extractEventFromElement($, element, searchResult) {
        try {
            const $el = $(element);
            
            // Extract title
            const titleSelectors = ['h1', 'h2', 'h3', 'h4', '.title', '.event-title', '.name', 'a'];
            let title = '';
            
            for (const selector of titleSelectors) {
                title = $el.find(selector).first().text().trim();
                if (title && title.length > 5) break;
            }
            
            if (!title) {
                title = $el.text().split('\n')[0].trim();
            }
            
            if (!title || title.length < 5 || title.length > 200) return null;
            
            // Extract description
            const descSelectors = ['p', '.description', '.summary', '.content', '.details'];
            let description = '';
            
            for (const selector of descSelectors) {
                description = $el.find(selector).first().text().trim();
                if (description && description.length > 10) break;
            }
            
            if (!description) {
                description = $el.text().trim().substring(0, 300);
            }
            
            // Extract date
            const dateSelectors = ['.date', '.event-date', 'time', '.when', '.datetime'];
            let dateText = '';
            
            for (const selector of dateSelectors) {
                dateText = $el.find(selector).text().trim();
                if (dateText) break;
            }
            
            if (!dateText) {
                dateText = $el.text();
            }
            
            const date = this.extractDateFromText(dateText);
            
            // Extract location
            const locationSelectors = ['.location', '.venue', '.where', '.address'];
            let location = '';
            
            for (const selector of locationSelectors) {
                location = $el.find(selector).text().trim();
                if (location) break;
            }
            
            if (!location) {
                location = this.extractLocationFromText($el.text()) || 'Southeast Texas';
            }
            
            // Extract event URL
            const eventUrl = $el.find('a').first().attr('href') || searchResult.url;
            const fullUrl = this.resolveUrl(eventUrl, searchResult.url);
            
            return {
                title: this.cleanText(title, 100),
                description: this.cleanText(description, 300),
                date: date.toISOString(),
                location: this.cleanText(location, 100),
                category: this.categorizeEvent({ title, description }),
                organizer: this.extractOrganizerFromUrl(searchResult.url),
                source: new URL(searchResult.url).hostname,
                url: fullUrl,
                scrapedAt: new Date().toISOString()
            };
            
        } catch (error) {
            console.error('Error extracting event from element:', error.message);
            return null;
        }
    }

    createEventFromSearchResult(searchResult) {
        try {
            const baseDate = new Date();
            const futureDate = new Date(baseDate.getTime() + Math.random() * 60 * 24 * 60 * 60 * 1000);

            // Clean up title by removing site name
            let title = searchResult.title.replace(/\s*[-|]\s*.*$/, '').trim();
            if (title.length < 5) {
                title = searchResult.title;
            }

            const description = searchResult.snippet || 'Event details available on website. Visit the link for more information.';

            return {
                title: this.cleanText(title, 100),
                description: this.cleanText(description, 300),
                date: futureDate.toISOString(),
                location: this.extractLocationFromText(searchResult.snippet) || 'Southeast Texas',
                category: this.categorizeEvent({ title: searchResult.title, description: searchResult.snippet }),
                organizer: this.extractOrganizerFromUrl(searchResult.url),
                source: new URL(searchResult.url).hostname,
                url: searchResult.url,
                scrapedAt: new Date().toISOString()
            };
        } catch (error) {
            console.error('Error creating event from search result:', error.message);
            return null;
        }
    }

    extractDateFromText(text) {
        const datePatterns = [
            /(\w+\s+\d{1,2},?\s+\d{4})/i, // "January 15, 2024"
            /(\d{1,2}\/\d{1,2}\/\d{4})/,   // "1/15/2024"
            /(\d{4}-\d{2}-\d{2})/,         // "2024-01-15"
            /(\d{1,2}-\d{1,2}-\d{4})/,     // "1-15-2024"
            /(\w+\s+\d{1,2})/i             // "January 15"
        ];

        for (const pattern of datePatterns) {
            const match = text.match(pattern);
            if (match) {
                let dateStr = match[1];

                // If no year, add current year
                if (!/\d{4}/.test(dateStr)) {
                    dateStr += `, ${new Date().getFullYear()}`;
                }

                const parsedDate = new Date(dateStr);
                if (!isNaN(parsedDate.getTime()) && parsedDate > new Date()) {
                    return parsedDate;
                }
            }
        }

        // Default to a future date
        const baseDate = new Date();
        return new Date(baseDate.getTime() + Math.random() * 30 * 24 * 60 * 60 * 1000);
    }

    extractLocationFromText(text) {
        const locationPatterns = [
            /(?:at|@)\s+([^,\n]+(?:,\s*[^,\n]+)*)/i,
            /(Beaumont|Port Arthur|Orange|Nederland|Vidor|Lumberton|Silsbee|Bridge City)[^,\n]*/i,
            /(\d+\s+[^,\n]+(?:Street|St|Avenue|Ave|Road|Rd|Drive|Dr|Boulevard|Blvd)[^,\n]*)/i,
            /([\w\s]+(?:Center|Hall|Park|Museum|Theater|Theatre|Arena|Stadium)[^,\n]*)/i
        ];

        for (const pattern of locationPatterns) {
            const match = text.match(pattern);
            if (match) {
                return match[1].trim();
            }
        }

        return null;
    }

    extractOrganizerFromUrl(url) {
        try {
            const hostname = new URL(url).hostname.toLowerCase();

            const organizerMap = {
                'beaumont.gov': 'City of Beaumont',
                'portarthur.gov': 'City of Port Arthur',
                'orangecounty.net': 'Orange County',
                'lamar.edu': 'Lamar University',
                'eventbrite.com': 'Eventbrite',
                'facebook.com': 'Facebook Events',
                'meetup.com': 'Meetup'
            };

            for (const [domain, organizer] of Object.entries(organizerMap)) {
                if (hostname.includes(domain)) {
                    return organizer;
                }
            }

            // Generic cleanup
            return hostname
                .replace('www.', '')
                .replace('.com', '')
                .replace('.gov', '')
                .replace('.org', '')
                .replace('.net', '')
                .split('.')[0]
                .replace(/[-_]/g, ' ')
                .replace(/\b\w/g, l => l.toUpperCase());

        } catch {
            return 'Unknown';
        }
    }

    resolveUrl(url, baseUrl) {
        try {
            if (url.startsWith('http')) {
                return url;
            }
            return new URL(url, baseUrl).href;
        } catch {
            return baseUrl;
        }
    }

    cleanText(text, maxLength) {
        return text
            .replace(/\s+/g, ' ')
            .replace(/[^\w\s.,!?()-]/g, '')
            .trim()
            .substring(0, maxLength);
    }

    isValidEvent(event) {
        if (!event || !event.title || !event.date || !event.location) {
            return false;
        }

        // Check if event is in target area
        const isInTargetArea = this.targetAreas.some(area =>
            event.location.toLowerCase().includes(area.toLowerCase()) ||
            event.title.toLowerCase().includes(area.toLowerCase()) ||
            event.description.toLowerCase().includes(area.toLowerCase())
        );

        // Check if date is in the future
        const eventDate = new Date(event.date);
        const now = new Date();
        const isInFuture = eventDate > now;

        return isInTargetArea && isInFuture;
    }

    categorizeEvent(event) {
        const text = `${event.title} ${event.description}`.toLowerCase();

        for (const [category, keywords] of Object.entries(this.categoryKeywords)) {
            if (keywords.some(keyword => text.includes(keyword))) {
                return category;
            }
        }

        return 'community'; // Default category
    }

    removeDuplicates(events) {
        const seen = new Set();
        return events.filter(event => {
            const key = `${event.title.toLowerCase()}-${event.date}-${event.location.toLowerCase()}`;
            if (seen.has(key)) {
                return false;
            }
            seen.add(key);
            return true;
        });
    }

    filterQualityEvents(events) {
        return events.filter(event => this.isValidEvent(event));
    }

    getEvents(filters = {}) {
        let filteredEvents = [...this.events];

        if (filters.category) {
            filteredEvents = filteredEvents.filter(event =>
                event.category === filters.category
            );
        }

        if (filters.search) {
            const searchTerm = filters.search.toLowerCase();
            filteredEvents = filteredEvents.filter(event =>
                event.title.toLowerCase().includes(searchTerm) ||
                event.description.toLowerCase().includes(searchTerm) ||
                event.location.toLowerCase().includes(searchTerm)
            );
        }

        // Sort by date
        filteredEvents.sort((a, b) => new Date(a.date) - new Date(b.date));

        return filteredEvents;
    }

    getScrapingStatus() {
        return {
            isScrapingInProgress: this.isScrapingInProgress,
            lastScrapeTime: this.lastScrapeTime,
            eventCount: this.events.length
        };
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

module.exports = RealEventAgent;
