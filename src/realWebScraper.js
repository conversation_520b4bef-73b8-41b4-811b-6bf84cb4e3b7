const cheerio = require('cheerio');

class RealWebScraper {
    constructor(webSearchTool, webFetchTool) {
        this.webSearch = webSearchTool;
        this.webFetch = webFetchTool;
        this.events = [];
        this.isScrapingInProgress = false;
        this.lastScrapeTime = null;
        
        // Southeast Texas cities and areas to focus on
        this.targetAreas = [
            'Beaumont TX',
            'Port Arthur TX', 
            'Orange TX',
            'Nederland TX',
            'Vidor TX',
            'Lumberton TX',
            'Silsbee TX',
            'Bridge City TX',
            'Southeast Texas',
            'Golden Triangle Texas'
        ];
        
        // Event categories mapping
        this.categoryKeywords = {
            business: ['business', 'networking', 'conference', 'seminar', 'workshop', 'job fair', 'career'],
            entertainment: ['concert', 'music', 'festival', 'show', 'performance', 'comedy', 'theater'],
            education: ['class', 'workshop', 'seminar', 'lecture', 'training', 'course', 'school'],
            sports: ['game', 'tournament', 'race', 'sports', 'athletic', 'fitness', 'run', 'walk'],
            community: ['community', 'volunteer', 'charity', 'fundraiser', 'meeting', 'cleanup'],
            arts: ['art', 'gallery', 'museum', 'craft', 'painting', 'sculpture', 'exhibition'],
            food: ['food', 'restaurant', 'dining', 'cooking', 'festival', 'taste', 'culinary'],
            health: ['health', 'wellness', 'medical', 'fitness', 'yoga', 'meditation', 'screening']
        };
    }

    async scrapeAllSources() {
        if (this.isScrapingInProgress) {
            console.log('Scraping already in progress...');
            return { status: 'already_running', events: this.events };
        }

        this.isScrapingInProgress = true;
        console.log('🔍 Starting REAL web scraping for Southeast Texas events...');
        
        try {
            const newEvents = [];

            // First scrape priority venues directly
            console.log('🏢 Starting priority venue scraping...');
            const venueEvents = await this.scrapePriorityVenues();
            newEvents.push(...venueEvents);

            // Search queries targeting Southeast Texas events
            const searchQueries = [
                'events Beaumont Texas 2024 2025 calendar',
                'Port Arthur Texas events festivals concerts',
                'Orange Texas community events calendar',
                'Southeast Texas Golden Triangle events',
                'Lamar University Beaumont events calendar',
                'Jefferson County Texas events meetings',
                'Beaumont civic center events schedule',
                'Southeast Texas business networking events',
                'Beaumont Port Arthur Orange festivals 2024',
                'Southeast Texas concerts shows entertainment',
                'Port Neches Texas events live music venues',
                'Baytown Texas events entertainment calendar',
                'Galveston Texas acoustic music venues events',
                'Neches River Brewing events Port Neches',
                'Lee College Baytown performing arts events',
                'Old Quarter Acoustic Cafe Galveston shows',
                'AR Entertainment Hub Baytown events',
                'Avenue Axe Port Neches events calendar'
            ];

            for (const query of searchQueries) {
                console.log(`🔍 Real web search: ${query}`);
                try {
                    const searchResults = await this.performRealWebSearch(query);
                    const scrapedEvents = await this.scrapeSearchResults(searchResults);
                    newEvents.push(...scrapedEvents);
                    
                    // Add delay between searches to be respectful
                    await this.delay(3000);
                } catch (error) {
                    console.error(`Error searching for "${query}":`, error.message);
                }
            }

            // Filter for quality first
            const filteredEvents = this.filterQualityEvents(newEvents);

            // Apply prioritization and deduplication (replaces old removeDuplicates)
            const prioritizedEvents = this.prioritizeAndDeduplicateEvents(filteredEvents);

            this.events = prioritizedEvents;
            this.lastScrapeTime = new Date();

            console.log(`✅ Real scraping completed! Found ${filteredEvents.length} total events, ${this.events.length} unique prioritized events`);
            
            return {
                status: 'success',
                events: this.events,
                count: this.events.length,
                lastScrapeTime: this.lastScrapeTime
            };
            
        } catch (error) {
            console.error('❌ Error during real scraping:', error);
            return {
                status: 'error',
                error: error.message,
                events: this.events
            };
        } finally {
            this.isScrapingInProgress = false;
        }
    }

    async performRealWebSearch(query) {
        try {
            console.log(`🌐 Performing real web search for: ${query}`);
            
            // Use the actual web search tool
            const searchResults = await this.webSearch({
                query: query,
                num_results: 8
            });
            
            console.log(`📊 Found ${searchResults.length} search results`);
            return searchResults;
            
        } catch (error) {
            console.error(`Web search failed for "${query}":`, error.message);
            return [];
        }
    }

    async scrapeSearchResults(searchResults) {
        const events = [];
        
        for (const result of searchResults) {
            try {
                console.log(`📄 Scraping: ${result.title}`);
                console.log(`🔗 URL: ${result.url}`);
                
                // Skip non-event URLs
                if (!this.isEventRelevantUrl(result.url, result.title)) {
                    console.log(`⏭️  Skipping non-event URL: ${result.url}`);
                    continue;
                }
                
                const pageEvents = await this.scrapeEventPage(result);
                events.push(...pageEvents);
                
                // Add delay between page scrapes
                await this.delay(2000);
                
            } catch (error) {
                console.error(`Error scraping ${result.url}:`, error.message);
                
                // Create fallback event from search result
                const fallbackEvent = this.createEventFromSearchResult(result);
                if (fallbackEvent) {
                    events.push(fallbackEvent);
                }
            }
        }
        
        return events;
    }

    isEventRelevantUrl(url, title) {
        const eventKeywords = [
            'event', 'calendar', 'festival', 'concert', 'meeting', 'conference',
            'workshop', 'seminar', 'show', 'performance', 'gathering', 'celebration'
        ];
        
        const locationKeywords = [
            'beaumont', 'port arthur', 'orange', 'southeast texas', 'golden triangle',
            'jefferson county', 'lamar university'
        ];
        
        const urlLower = url.toLowerCase();
        const titleLower = title.toLowerCase();
        const combined = urlLower + ' ' + titleLower;
        
        const hasEventKeyword = eventKeywords.some(keyword => combined.includes(keyword));
        const hasLocationKeyword = locationKeywords.some(keyword => combined.includes(keyword));
        
        // Skip social media posts, news articles, and excluded platforms
        const skipPatterns = [
            'facebook.com/posts', 'twitter.com', 'instagram.com',
            'news', 'article', 'blog', 'wikipedia', 'yelp',
            'eventbrite.com', 'meetup.com'
        ];
        
        const shouldSkip = skipPatterns.some(pattern => urlLower.includes(pattern));
        
        return hasEventKeyword && hasLocationKeyword && !shouldSkip;
    }

    async scrapeEventPage(searchResult) {
        try {
            console.log(`🌐 Fetching content from: ${searchResult.url}`);
            
            // Use the actual web fetch tool
            const htmlContent = await this.webFetch({
                url: searchResult.url
            });
            
            console.log(`📝 Received ${htmlContent.length} characters of content`);
            
            return this.parseEventContent(htmlContent, searchResult);
            
        } catch (error) {
            console.error(`Failed to fetch ${searchResult.url}:`, error.message);
            
            // Return fallback event based on search result
            const fallbackEvent = this.createEventFromSearchResult(searchResult);
            return fallbackEvent ? [fallbackEvent] : [];
        }
    }

    parseEventContent(htmlContent, searchResult) {
        const events = [];
        
        try {
            const $ = cheerio.load(htmlContent);
            
            // Remove script and style tags
            $('script, style, nav, footer, header').remove();
            
            // Look for event-specific patterns
            const eventSelectors = [
                '.event', '.calendar-event', '.event-item', '.event-listing',
                '[class*="event"]', '.listing', '.post', '.entry',
                '.calendar-item', '.schedule-item', 'article'
            ];
            
            let foundEvents = false;
            
            for (const selector of eventSelectors) {
                const elements = $(selector);
                if (elements.length > 0) {
                    console.log(`📋 Found ${elements.length} potential events with selector: ${selector}`);
                    foundEvents = true;
                    
                    elements.each((i, element) => {
                        // Remove the 10 event limit per page
                        const event = this.extractEventFromElement($, element, searchResult);
                        if (event) {
                            // If this is a generic community event, try to extract specific events from it
                            if (this.isGenericCommunityEvent(event)) {
                                console.log(`🔍 Detected community event page, extracting specific events: ${event.title}`);
                                const specificEvents = this.extractSpecificEventsFromCommunityPage($, element, searchResult);
                                events.push(...specificEvents);
                            } else if (this.isValidEvent(event)) {
                                events.push(event);
                            }
                        }
                    });
                    
                    if (events.length > 0) break; // Use first successful selector
                }
            }
            
            // If no structured events found, try to extract from page content
            if (!foundEvents || events.length === 0) {
                console.log('📄 No structured events found, creating from search result');
                const event = this.createEventFromSearchResult(searchResult);
                if (event && this.isValidEvent(event)) {
                    events.push(event);
                }
            }
            
        } catch (error) {
            console.error('Error parsing HTML content:', error.message);
            // Fallback to search result
            const event = this.createEventFromSearchResult(searchResult);
            if (event && this.isValidEvent(event)) {
                events.push(event);
            }
        }
        
        console.log(`✅ Extracted ${events.length} valid events from page`);
        return events;
    }

    extractEventFromElement($, element, searchResult) {
        try {
            const $el = $(element);

            // Extract title
            const titleSelectors = ['h1', 'h2', 'h3', 'h4', '.title', '.event-title', '.name', 'a'];
            let title = '';

            for (const selector of titleSelectors) {
                title = $el.find(selector).first().text().trim();
                if (title && title.length > 5) break;
            }

            if (!title) {
                title = $el.text().split('\n')[0].trim();
            }

            if (!title || title.length < 5 || title.length > 200) return null;

            // Extract description
            const descSelectors = ['p', '.description', '.summary', '.content', '.details'];
            let description = '';

            for (const selector of descSelectors) {
                description = $el.find(selector).first().text().trim();
                if (description && description.length > 10) break;
            }

            if (!description) {
                description = $el.text().trim().substring(0, 300);
            }

            // Extract date
            const dateSelectors = ['.date', '.event-date', 'time', '.when', '.datetime'];
            let dateText = '';

            for (const selector of dateSelectors) {
                dateText = $el.find(selector).text().trim();
                if (dateText) break;
            }

            if (!dateText) {
                dateText = $el.text();
            }

            const date = this.extractDateFromText(dateText);

            // Extract location
            const locationSelectors = ['.location', '.venue', '.where', '.address'];
            let location = '';

            for (const selector of locationSelectors) {
                location = $el.find(selector).text().trim();
                if (location) break;
            }

            if (!location) {
                location = this.extractLocationFromText($el.text()) || 'Southeast Texas';
            }

            // Extract event URL
            const eventUrl = $el.find('a').first().attr('href') || searchResult.url;
            const fullUrl = this.resolveUrl(eventUrl, searchResult.url);

            return {
                title: this.cleanText(title, 100),
                description: this.cleanText(description, 300),
                date: date.toISOString(),
                location: this.cleanText(location, 100),
                category: this.categorizeEvent({ title, description }),
                organizer: this.extractOrganizerFromUrl(searchResult.url),
                source: new URL(searchResult.url).hostname,
                url: fullUrl,
                scrapedAt: new Date().toISOString()
            };

        } catch (error) {
            console.error('Error extracting event from element:', error.message);
            return null;
        }
    }

    createEventFromSearchResult(searchResult) {
        try {
            const baseDate = new Date();
            const futureDate = new Date(baseDate.getTime() + Math.random() * 60 * 24 * 60 * 60 * 1000);

            // Clean up title by removing site name
            let title = searchResult.title.replace(/\s*[-|]\s*.*$/, '').trim();
            if (title.length < 5) {
                title = searchResult.title;
            }

            const description = searchResult.snippet || 'Event details available on website. Visit the link for more information.';

            return {
                title: this.cleanText(title, 100),
                description: this.cleanText(description, 300),
                date: futureDate.toISOString(),
                location: this.extractLocationFromText(searchResult.snippet) || 'Southeast Texas',
                category: this.categorizeEvent({ title: searchResult.title, description: searchResult.snippet }),
                organizer: this.extractOrganizerFromUrl(searchResult.url),
                source: new URL(searchResult.url).hostname,
                url: searchResult.url,
                scrapedAt: new Date().toISOString()
            };
        } catch (error) {
            console.error('Error creating event from search result:', error.message);
            return null;
        }
    }

    extractDateFromText(text) {
        const datePatterns = [
            /(\w+\s+\d{1,2},?\s+\d{4})/i, // "January 15, 2024"
            /(\d{1,2}\/\d{1,2}\/\d{4})/,   // "1/15/2024"
            /(\d{4}-\d{2}-\d{2})/,         // "2024-01-15"
            /(\d{1,2}-\d{1,2}-\d{4})/,     // "1-15-2024"
            /(\w+\s+\d{1,2})/i             // "January 15"
        ];

        for (const pattern of datePatterns) {
            const match = text.match(pattern);
            if (match) {
                let dateStr = match[1];

                // If no year, add current year
                if (!/\d{4}/.test(dateStr)) {
                    dateStr += `, ${new Date().getFullYear()}`;
                }

                const parsedDate = new Date(dateStr);
                if (!isNaN(parsedDate.getTime()) && parsedDate > new Date()) {
                    return parsedDate;
                }
            }
        }

        // Default to a future date
        const baseDate = new Date();
        return new Date(baseDate.getTime() + Math.random() * 30 * 24 * 60 * 60 * 1000);
    }

    extractLocationFromText(text) {
        const locationPatterns = [
            /(?:at|@)\s+([^,\n]+(?:,\s*[^,\n]+)*)/i,
            /(Beaumont|Port Arthur|Orange|Nederland|Vidor|Lumberton|Silsbee|Bridge City)[^,\n]*/i,
            /(\d+\s+[^,\n]+(?:Street|St|Avenue|Ave|Road|Rd|Drive|Dr|Boulevard|Blvd)[^,\n]*)/i,
            /([\w\s]+(?:Center|Hall|Park|Museum|Theater|Theatre|Arena|Stadium)[^,\n]*)/i
        ];

        for (const pattern of locationPatterns) {
            const match = text.match(pattern);
            if (match) {
                return match[1].trim();
            }
        }

        return null;
    }

    extractOrganizerFromUrl(url) {
        try {
            const hostname = new URL(url).hostname.toLowerCase();

            const organizerMap = {
                'beaumont.gov': 'City of Beaumont',
                'portarthur.gov': 'City of Port Arthur',
                'orangetx.gov': 'City of Orange, TX',
                'lamar.edu': 'Lamar University',
                'facebook.com': 'Facebook Events'
            };

            for (const [domain, organizer] of Object.entries(organizerMap)) {
                if (hostname.includes(domain)) {
                    return organizer;
                }
            }

            // Generic cleanup
            return hostname
                .replace('www.', '')
                .replace('.com', '')
                .replace('.gov', '')
                .replace('.org', '')
                .replace('.net', '')
                .split('.')[0]
                .replace(/[-_]/g, ' ')
                .replace(/\b\w/g, l => l.toUpperCase());

        } catch {
            return 'Unknown';
        }
    }

    resolveUrl(url, baseUrl) {
        try {
            if (url.startsWith('http')) {
                return url;
            }
            return new URL(url, baseUrl).href;
        } catch {
            return baseUrl;
        }
    }

    cleanText(text, maxLength) {
        return text
            .replace(/\s+/g, ' ')
            .replace(/[^\w\s.,!?()-]/g, '')
            .trim()
            .substring(0, maxLength);
    }

    isGenericCommunityEvent(event) {
        if (!event || !event.title) return false;

        const title = event.title.toLowerCase();
        const description = (event.description || '').toLowerCase();

        const communityEventPatterns = [
            'community event',
            'local community event',
            'community calendar',
            'events calendar',
            'upcoming events',
            'event listings'
        ];

        return communityEventPatterns.some(pattern =>
            title.includes(pattern) || description.includes(pattern)
        );
    }

    extractSpecificEventsFromCommunityPage($, element, searchResult) {
        const specificEvents = [];
        const $el = $(element);

        // Look for event listings within the community event page
        const eventListSelectors = [
            '.event-item',
            '.event-listing',
            '.calendar-event',
            'li:contains("2024")',
            'li:contains("2025")',
            'div:contains("January")',
            'div:contains("February")',
            'div:contains("March")',
            'div:contains("April")',
            'div:contains("May")',
            'div:contains("June")',
            'div:contains("July")',
            'div:contains("August")',
            'div:contains("September")',
            'div:contains("October")',
            'div:contains("November")',
            'div:contains("December")'
        ];

        for (const selector of eventListSelectors) {
            const eventElements = $el.find(selector);
            if (eventElements.length > 0) {
                console.log(`📋 Found ${eventElements.length} specific events with selector: ${selector}`);

                eventElements.each((i, eventEl) => {
                    const specificEvent = this.extractEventFromElement($, eventEl, searchResult);
                    if (specificEvent && this.isValidEvent(specificEvent) && !this.isGenericCommunityEvent(specificEvent)) {
                        specificEvents.push(specificEvent);
                    }
                });

                if (specificEvents.length > 0) {
                    break; // Found events, no need to try other selectors
                }
            }
        }

        // If no specific events found, try to parse text content for event information
        if (specificEvents.length === 0) {
            const textEvents = this.extractEventsFromText($el.text(), searchResult);
            specificEvents.push(...textEvents);
        }

        console.log(`🎯 Extracted ${specificEvents.length} specific events from community page`);
        return specificEvents;
    }

    extractEventsFromText(text, searchResult) {
        const events = [];
        const lines = text.split('\n').map(line => line.trim()).filter(line => line.length > 10);

        for (const line of lines) {
            // Look for lines that contain dates and event information
            const dateMatch = line.match(/(\w+\s+\d{1,2},?\s+\d{4}|\d{1,2}\/\d{1,2}\/\d{4})/i);
            if (dateMatch && line.length > 20) {
                const eventTitle = line.replace(dateMatch[0], '').trim();
                if (eventTitle.length > 5 && !this.isGenericTitle(eventTitle)) {
                    const event = {
                        title: this.cleanText(eventTitle, 100),
                        description: `Event details available on website. Visit the link for more information.`,
                        date: this.extractDateFromText(line).toISOString(),
                        location: this.extractLocationFromText(line) || 'Southeast Texas',
                        category: this.categorizeEvent({ title: eventTitle, description: '' }),
                        organizer: this.extractOrganizerFromUrl(searchResult.url),
                        source: new URL(searchResult.url).hostname,
                        url: searchResult.url,
                        scrapedAt: new Date().toISOString()
                    };

                    if (this.isValidEvent(event)) {
                        events.push(event);
                    }
                }
            }
        }

        return events;
    }

    isGenericTitle(title) {
        const genericTitles = [
            'community event',
            'local event',
            'upcoming event',
            'event details',
            'more information',
            'check back',
            'details coming soon'
        ];

        const lowerTitle = title.toLowerCase();
        return genericTitles.some(generic => lowerTitle.includes(generic));
    }

    isValidEvent(event) {
        if (!event || !event.title || !event.date || !event.location) {
            return false;
        }

        const title = event.title.toLowerCase();
        const description = event.description.toLowerCase();

        // Filter out calendar/technical files - these aren't real events
        const calendarFilePatterns = [
            'export .ics file',
            'icalendar',
            'calendar export',
            'download calendar',
            '.ics',
            'add to calendar',
            'calendar file',
            'export calendar',
            'calendar download'
        ];

        const isCalendarFile = calendarFilePatterns.some(pattern =>
            title.includes(pattern) || description.includes(pattern)
        );

        if (isCalendarFile) {
            console.log(`🗓️ Filtering out calendar file (not an event): ${event.title}`);
            return false;
        }

        // Filter out truly generic/placeholder events
        const placeholderPatterns = [
            'check back for more details',
            'details coming soon',
            'more information to follow',
            'event details tbd',
            'placeholder event',
            'generic event'
        ];

        const isPlaceholder = placeholderPatterns.some(pattern =>
            title.includes(pattern) || description.includes(pattern)
        );

        if (isPlaceholder) {
            console.log(`🚫 Filtering out placeholder event: ${event.title}`);
            return false;
        }

        // Filter out events with vague organizers like "Setxchamber" without details
        if (event.organizer && event.organizer.toLowerCase() === 'setxchamber' &&
            description.includes('check back for more details')) {
            console.log(`🚫 Filtering out placeholder Setxchamber event: ${event.title}`);
            return false;
        }

        // Check if event is in target area
        const isInTargetArea = this.targetAreas.some(area =>
            event.location.toLowerCase().includes(area.toLowerCase()) ||
            event.title.toLowerCase().includes(area.toLowerCase()) ||
            event.description.toLowerCase().includes(area.toLowerCase())
        );

        // Check if date is in the future
        const eventDate = new Date(event.date);
        const now = new Date();
        const isInFuture = eventDate > now;

        return isInTargetArea && isInFuture;
    }

    categorizeEvent(event) {
        const text = `${event.title} ${event.description}`.toLowerCase();

        for (const [category, keywords] of Object.entries(this.categoryKeywords)) {
            if (keywords.some(keyword => text.includes(keyword))) {
                return category;
            }
        }

        return 'community'; // Default category
    }

    removeDuplicates(events) {
        const seen = new Set();
        return events.filter(event => {
            const key = `${event.title.toLowerCase()}-${event.date}-${event.location.toLowerCase()}`;
            if (seen.has(key)) {
                return false;
            }
            seen.add(key);
            return true;
        });
    }

    filterQualityEvents(events) {
        return events.filter(event => this.isValidEvent(event));
    }

    prioritizeAndDeduplicateEvents(events) {
        console.log(`🔄 Prioritizing and deduplicating ${events.length} events...`);

        // Step 1: Add source priority scores
        const eventsWithPriority = events.map(event => ({
            ...event,
            sourcePriority: this.getSourcePriority(event.source),
            sourceType: this.getSourceType(event.source)
        }));

        // Step 2: Group potential duplicates
        const eventGroups = this.groupSimilarEvents(eventsWithPriority);

        // Step 3: Select best event from each group
        const deduplicatedEvents = eventGroups.map(group => {
            if (group.length === 1) {
                return group[0];
            }

            // Sort by priority (higher is better) and select the best one
            const sortedGroup = group.sort((a, b) => {
                // Primary sort: source priority
                if (a.sourcePriority !== b.sourcePriority) {
                    return b.sourcePriority - a.sourcePriority;
                }

                // Secondary sort: completeness of information
                const aCompleteness = this.calculateEventCompleteness(a);
                const bCompleteness = this.calculateEventCompleteness(b);
                if (aCompleteness !== bCompleteness) {
                    return bCompleteness - aCompleteness;
                }

                // Tertiary sort: recency of scraping
                return new Date(b.scrapedAt || 0) - new Date(a.scrapedAt || 0);
            });

            const bestEvent = sortedGroup[0];
            console.log(`🎯 Selected best version of "${bestEvent.title}" from ${bestEvent.sourceType} (priority ${bestEvent.sourcePriority})`);

            return bestEvent;
        });

        // Step 4: Sort final results by source priority
        const finalEvents = deduplicatedEvents.sort((a, b) => {
            // Primary sort: source priority
            if (a.sourcePriority !== b.sourcePriority) {
                return b.sourcePriority - a.sourcePriority;
            }

            // Secondary sort: event date
            return new Date(a.date) - new Date(b.date);
        });

        console.log(`✨ Prioritization complete: ${events.length} → ${finalEvents.length} events`);
        this.logPrioritizationStats(finalEvents);

        return finalEvents;
    }

    getEvents(filters = {}) {
        let filteredEvents = [...this.events];

        if (filters.category) {
            filteredEvents = filteredEvents.filter(event =>
                event.category === filters.category
            );
        }

        if (filters.search) {
            const searchTerm = filters.search.toLowerCase();
            filteredEvents = filteredEvents.filter(event =>
                event.title.toLowerCase().includes(searchTerm) ||
                event.description.toLowerCase().includes(searchTerm) ||
                event.location.toLowerCase().includes(searchTerm)
            );
        }

        // Sort by date
        filteredEvents.sort((a, b) => new Date(a.date) - new Date(b.date));

        return filteredEvents;
    }

    getScrapingStatus() {
        return {
            isScrapingInProgress: this.isScrapingInProgress,
            lastScrapeTime: this.lastScrapeTime,
            eventCount: this.events.length
        };
    }

    async scrapePriorityVenues() {
        console.log('🏢 Starting priority venue scraping...');

        const priorityVenueUrls = [
            // Jefferson Theatre - Top Priority
            'https://www.jeffersontheatre.org/events',
            'https://beaumonteventstx.com/venue/jefferson-theatre',

            // Downtown Event Centre
            'https://beaumonteventstx.com/venue/downtown-event-centre',

            // The Logon Café
            'https://logoncafe.net/events',

            // Lutcher Theater
            'https://lutcher.org/events',

            // The Grand 1894 Opera House
            'https://thegrand.com/calendar-of-events-2',

            // Neches River Brewing
            'https://www.nechesriverbrewing.com/events',

            // Beaumont CVB venues
            'https://beaumontcvb.com/events'
        ];

        const venueEvents = [];

        for (const url of priorityVenueUrls) {
            try {
                console.log(`🎭 Scraping priority venue: ${url}`);

                // Create a mock search result for the venue
                const venueResult = {
                    title: this.getVenueNameFromUrl(url),
                    url: url,
                    snippet: 'Priority venue events calendar'
                };

                const events = await this.scrapeSearchResults([venueResult]);

                if (events.length > 0) {
                    console.log(`✅ Found ${events.length} events from ${url}`);
                    venueEvents.push(...events);
                } else {
                    console.log(`📭 No events found at ${url}`);
                }

                // Respectful delay between venue scrapes
                await this.delay(2000);

            } catch (error) {
                console.error(`❌ Error scraping venue ${url}:`, error.message);
            }
        }

        return venueEvents;
    }

    getVenueNameFromUrl(url) {
        if (url.includes('jeffersontheatre')) return 'Jefferson Theatre Events';
        if (url.includes('beaumonteventstx.com/venue/jefferson-theatre')) return 'Jefferson Theatre';
        if (url.includes('beaumonteventstx.com/venue/downtown-event-centre')) return 'Downtown Event Centre';
        if (url.includes('logoncafe')) return 'The Logon Café Events';
        if (url.includes('lutcher.org')) return 'Lutcher Theater Events';
        if (url.includes('thegrand.com')) return 'The Grand 1894 Opera House';
        if (url.includes('nechesriverbrewing')) return 'Neches River Brewing Events';
        if (url.includes('beaumontcvb')) return 'Beaumont CVB Events';
        return 'Venue Events';
    }

    getSourcePriority(source) {
        // Tier 1: Direct venue websites (highest priority)
        const venueWebsites = [
            'lutcher.org',
            'sost.org',
            'beaumontciviccenter.com',
            'lamar.edu',
            'spindletop.org',
            'mcfaddin-ward.org',
            'amset.org',
            'fordpark.com',
            'jeffersontheatre.org',
            'julierogerstheatre.org',
            'arsentertainmenthub.com',
            'lee.edu',
            'oldquarteracousticcafe.com',
            'visitportarthurtx.com',
            'beaumonteventstx.com',
            'logoncafe.net',
            'thegrand.com',
            'nechesriverbrewing.com'
        ];

        // Tier 2: Venue social media
        const venueSocialMedia = [
            'facebook.com/LutcherTheater',
            'facebook.com/BeaumontCivicCenter',
            'facebook.com/LamarUniversity',
            'facebook.com/JeffersonTheatre'
        ];

        // Tier 3: Official city/government sources
        const officialSources = [
            'beaumont.gov',
            'portarthur.gov',
            'orangetx.gov',
            'beaumontcvb.com'
        ];

        // Tier 4: Social media event platforms
        const socialPlatforms = [
            'facebook.com/events'
        ];

        const sourceUrl = source.toLowerCase();

        if (venueWebsites.some(domain => sourceUrl.includes(domain))) {
            return 100; // Highest priority - direct venue websites
        }

        if (venueSocialMedia.some(domain => sourceUrl.includes(domain))) {
            return 90; // High priority - venue social media
        }

        if (officialSources.some(domain => sourceUrl.includes(domain))) {
            return 80; // High priority - official sources
        }

        if (socialPlatforms.some(domain => sourceUrl.includes(domain))) {
            return 60; // Medium priority - social platforms
        }

        // Default priority for other sources
        return 50;
    }

    getSourceType(source) {
        const sourceUrl = source.toLowerCase();

        if (sourceUrl.includes('facebook.com') && !sourceUrl.includes('/events/search')) {
            return 'venue-social-media';
        }

        // Removed eventbrite and meetup - no longer using these sources

        if (sourceUrl.includes('.gov') || sourceUrl.includes('cvb.com')) {
            return 'official-source';
        }

        if (sourceUrl.includes('.org') || sourceUrl.includes('.edu')) {
            return 'venue-website';
        }

        return 'other-source';
    }

    groupSimilarEvents(events) {
        const groups = [];
        const processed = new Set();

        events.forEach((event, index) => {
            if (processed.has(index)) return;

            const similarEvents = [event];
            processed.add(index);

            // Find similar events
            events.forEach((otherEvent, otherIndex) => {
                if (otherIndex <= index || processed.has(otherIndex)) return;

                if (this.areEventsSimilar(event, otherEvent)) {
                    similarEvents.push(otherEvent);
                    processed.add(otherIndex);
                }
            });

            groups.push(similarEvents);
        });

        return groups;
    }

    areEventsSimilar(event1, event2) {
        // Check title similarity
        const title1 = event1.title.toLowerCase().trim();
        const title2 = event2.title.toLowerCase().trim();

        // Exact title match
        if (title1 === title2) {
            return true;
        }

        // Similar titles (80% similarity)
        const titleSimilarity = this.calculateStringSimilarity(title1, title2);
        if (titleSimilarity > 0.8) {
            // Also check date proximity (within 1 day)
            const date1 = new Date(event1.date);
            const date2 = new Date(event2.date);
            const daysDiff = Math.abs(date1 - date2) / (1000 * 60 * 60 * 24);

            if (daysDiff <= 1) {
                return true;
            }
        }

        return false;
    }

    calculateStringSimilarity(str1, str2) {
        const longer = str1.length > str2.length ? str1 : str2;
        const shorter = str1.length > str2.length ? str2 : str1;

        if (longer.length === 0) return 1.0;

        const editDistance = this.levenshteinDistance(longer, shorter);
        return (longer.length - editDistance) / longer.length;
    }

    levenshteinDistance(str1, str2) {
        const matrix = [];

        for (let i = 0; i <= str2.length; i++) {
            matrix[i] = [i];
        }

        for (let j = 0; j <= str1.length; j++) {
            matrix[0][j] = j;
        }

        for (let i = 1; i <= str2.length; i++) {
            for (let j = 1; j <= str1.length; j++) {
                if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                    matrix[i][j] = matrix[i - 1][j - 1];
                } else {
                    matrix[i][j] = Math.min(
                        matrix[i - 1][j - 1] + 1,
                        matrix[i][j - 1] + 1,
                        matrix[i - 1][j] + 1
                    );
                }
            }
        }

        return matrix[str2.length][str1.length];
    }

    calculateEventCompleteness(event) {
        let score = 0;

        if (event.title && event.title.length > 10) score += 20;
        if (event.description && event.description.length > 50) score += 25;
        if (event.date) score += 20;
        if (event.location && event.location.length > 10) score += 15;
        if (event.organizer) score += 10;
        if (event.url) score += 5;
        if (event.phone) score += 3;
        if (event.email) score += 2;

        return score;
    }

    logPrioritizationStats(events) {
        const stats = {
            'venue-website': 0,
            'venue-social-media': 0,
            'official-source': 0,
            'social-platform': 0,
            'other-source': 0
        };

        events.forEach(event => {
            stats[event.sourceType] = (stats[event.sourceType] || 0) + 1;
        });

        console.log('📊 Final event distribution by source type:');
        Object.entries(stats).forEach(([type, count]) => {
            if (count > 0) {
                console.log(`   ${type}: ${count} events`);
            }
        });
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

module.exports = RealWebScraper;
