const express = require('express');
const router = express.Router();

// GET /api/events - Get all events with optional filtering
router.get('/', async (req, res) => {
    try {
        const { category, search } = req.query;
        const eventScraper = req.app.locals.eventScraper;
        
        const filters = {};
        if (category) filters.category = category;
        if (search) filters.search = search;
        
        const events = eventScraper.getEvents(filters);
        
        res.json({
            success: true,
            events,
            count: events.length,
            lastScrapeTime: eventScraper.lastScrapeTime
        });
    } catch (error) {
        console.error('Error fetching events:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch events',
            message: error.message
        });
    }
});

// POST /api/events/scrape - Trigger new scraping
router.post('/scrape', async (req, res) => {
    try {
        const eventScraper = req.app.locals.eventScraper;
        
        // Check if scraping is already in progress
        const status = eventScraper.getScrapingStatus();
        if (status.isScrapingInProgress) {
            return res.json({
                success: true,
                message: 'Scraping already in progress',
                status: 'running',
                eventCount: status.eventCount
            });
        }
        
        // Start scraping and enhancement (don't wait for completion)
        eventScraper.scrapeAllSources()
            .then(async (scrapedEvents) => {
                console.log(`Scraping completed: ${scrapedEvents.length} events found`);

                // Enhance the scraped events and associate with venues
                const eventEnhancer = req.app.locals.eventEnhancer;
                const enhancedEvents = await eventEnhancer.enhanceEventsFromOriginal(scrapedEvents);
                console.log(`Enhanced ${enhancedEvents.length} events with venue associations`);
            })
            .catch(error => {
                console.error('Scraping failed:', error);
            });
        
        res.json({
            success: true,
            message: 'Event scraping started',
            status: 'started'
        });
        
    } catch (error) {
        console.error('Error starting scrape:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to start scraping',
            message: error.message
        });
    }
});

// GET /api/events/status - Get scraping status
router.get('/status', (req, res) => {
    try {
        const eventScraper = req.app.locals.eventScraper;
        const status = eventScraper.getScrapingStatus();
        
        res.json({
            success: true,
            ...status
        });
    } catch (error) {
        console.error('Error getting status:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to get status',
            message: error.message
        });
    }
});

// GET /api/events/categories - Get available categories
router.get('/categories', (req, res) => {
    try {
        const categories = [
            { value: '', label: 'All Categories' },
            { value: 'business', label: 'Business' },
            { value: 'entertainment', label: 'Entertainment' },
            { value: 'education', label: 'Education' },
            { value: 'sports', label: 'Sports' },
            { value: 'community', label: 'Community' },
            { value: 'arts', label: 'Arts & Culture' },
            { value: 'food', label: 'Food & Dining' },
            { value: 'health', label: 'Health & Wellness' }
        ];

        res.json({
            success: true,
            categories
        });
    } catch (error) {
        console.error('Error getting categories:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to get categories',
            message: error.message
        });
    }
});

module.exports = router;
