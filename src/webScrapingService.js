const RealWebScraper = require('./realWebScraper');

class WebScrapingService {
    constructor(webSearchFunction, webFetchFunction) {
        // Store the actual web tools - REQUIRED, NO FALLBACKS
        this.webSearchFunction = webSearchFunction;
        this.webFetchFunction = webFetchFunction;

        if (!webSearchFunction || !webFetchFunction) {
            throw new Error('Real web search and fetch functions are required - NO SIMULATIONS');
        }

        // Create web scraper with real web tools
        this.scraper = new RealWebScraper(
            this.webSearchTool.bind(this),
            this.webFetchTool.bind(this)
        );

        console.log('🌐 WebScrapingService initialized with REAL web tools only');
    }

    async webSearchTool(params) {
        try {
            console.log(`�� Real web search: ${params.query}`);

            const results = await this.webSearchFunction({
                query: params.query,
                num_results: params.num_results || 8
            });
            
            console.log(`📊 Found ${results.length} REAL search results`);
            return results;

        } catch (error) {
            console.error('Real web search error:', error);
            throw error; // Fail properly - no fallbacks
        }
    }

    async webFetchTool(params) {
        try {
            console.log(`🌐 Real web fetch: ${params.url}`);

            const content = await this.webFetchFunction({
                url: params.url
            });
            
            console.log(`📄 Fetched ${content.length} characters of REAL content`);
            return content;

        } catch (error) {
            console.error('Real web fetch error:', error);
            throw error; // Fail properly - no fallbacks
        }
    }

    async scrapeAllSources() {
        return await this.scraper.scrapeAllSources();
    }

    getEvents(filters = {}) {
        return this.scraper.getEvents(filters);
    }

    getScrapingStatus() {
        return this.scraper.getScrapingStatus();
    }
}

module.exports = WebScrapingService;
