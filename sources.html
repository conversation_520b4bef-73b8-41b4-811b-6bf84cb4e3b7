<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data Sources - Southeast Texas Events</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            color: #333;
            line-height: 1.6;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px 0;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 3em;
            font-weight: bold;
            margin-bottom: 15px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
            margin-bottom: 30px;
        }
        
        .nav-links {
            display: flex;
            justify-content: center;
            gap: 30px;
            flex-wrap: wrap;
        }
        
        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 12px 24px;
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 25px;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .nav-links a:hover {
            background: rgba(255,255,255,0.2);
            border-color: rgba(255,255,255,0.6);
            transform: translateY(-2px);
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .sources-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .source-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        
        .source-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 50px rgba(0,0,0,0.15);
        }
        
        .source-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 20px;
        }
        
        .source-title {
            font-size: 1.4em;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }
        
        .source-priority {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: bold;
        }
        
        .source-type {
            background: #e9ecef;
            color: #495057;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 0.85em;
            display: inline-block;
            margin-bottom: 15px;
            text-transform: capitalize;
        }
        
        .source-description {
            color: #666;
            margin-bottom: 20px;
            line-height: 1.6;
        }
        
        .source-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .detail-item {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        
        .detail-label {
            font-size: 0.8em;
            font-weight: 600;
            color: #666;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .detail-value {
            font-weight: 500;
            color: #333;
        }
        
        .source-coverage {
            margin-bottom: 20px;
        }
        
        .coverage-title {
            font-size: 0.9em;
            font-weight: 600;
            color: #555;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .coverage-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        
        .coverage-tag {
            background: #f8f9fa;
            color: #495057;
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 0.8em;
            border: 1px solid #e9ecef;
        }
        
        .source-actions {
            display: flex;
            gap: 10px;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            flex: 1;
            font-size: 0.9em;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        
        .btn-outline {
            background: transparent;
            color: #667eea;
            border: 2px solid #667eea;
        }
        
        .btn-outline:hover {
            background: #667eea;
            color: white;
        }
        
        .verified-badge {
            background: #d4edda;
            color: #155724;
            padding: 4px 8px;
            border-radius: 8px;
            font-size: 0.7em;
            font-weight: bold;
            display: inline-block;
            margin-left: 10px;
        }
        
        .contact-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
        }
        
        .contact-title {
            color: #856404;
            font-weight: 600;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .contact-details {
            color: #856404;
            font-size: 0.9em;
            line-height: 1.5;
        }
        
        .section-title {
            font-size: 2.2em;
            font-weight: bold;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .tier-section {
            margin-bottom: 50px;
        }
        
        .tier-title {
            font-size: 1.6em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 3px solid #667eea;
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2.2em;
            }
            
            .sources-grid {
                grid-template-columns: 1fr;
            }
            
            .source-details {
                grid-template-columns: 1fr;
            }
            
            .source-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <h1>📊 Data Sources</h1>
            <p>Comprehensive Southeast Texas Event Data Sources & Scraping Strategies</p>
            <div class="nav-links">
                <a href="/">🏠 Home</a>
                <a href="/venues.html">🏢 Venues</a>
                <a href="/admin.html">⚙️ Admin</a>
                <a href="#tier1">🥇 Tier 1 Sources</a>
                <a href="#community">👥 Community Contacts</a>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="section-title">Southeast Texas Event Data Sources</div>
        
        <div id="tier1" class="tier-section">
            <div class="tier-title">🥇 Tier 1: Official & High-Priority Sources</div>
            <div class="sources-grid" id="tier1Sources"></div>
        </div>
        
        <div id="tier2" class="tier-section">
            <div class="tier-title">🥈 Tier 2: Venue-Specific Sources</div>
            <div class="sources-grid" id="tier2Sources"></div>
        </div>
        
        <div id="community" class="tier-section">
            <div class="tier-title">👥 Community Contacts & Manual Sources</div>
            <div class="sources-grid" id="communitySources"></div>
        </div>
    </div>

    <script>
        class DataSourcesPage {
            constructor() {
                this.apiBaseUrl = window.location.origin;
                this.init();
            }

            async init() {
                await this.loadDataSources();
            }

            async loadDataSources() {
                try {
                    // For now, we'll use the comprehensive data we know about
                    this.renderDataSources();
                } catch (error) {
                    console.error('Error loading data sources:', error);
                }
            }

            renderDataSources() {
                this.renderTier1Sources();
                this.renderTier2Sources();
                this.renderCommunityContacts();
            }

            renderTier1Sources() {
                const tier1Sources = [
                    {
                        name: 'City of Beaumont Events Calendar',
                        url: 'https://beaumonttexas.gov/calendar.aspx?CID=38',
                        type: 'official-city',
                        priority: 10,
                        description: 'Comprehensive city-wide events including community centers, parks, and public venues',
                        coverage: ['community-centers', 'parks-recreation', 'city-events', 'public-venues'],
                        updateFrequency: 'Daily',
                        reliability: 'High',
                        verified: true
                    },
                    {
                        name: 'Beaumont Convention and Visitors Bureau',
                        url: 'https://beaumontcvb.com/events/',
                        type: 'tourism-bureau',
                        priority: 10,
                        description: 'Wide range of events including festivals, concerts, and community gatherings',
                        coverage: ['festivals', 'concerts', 'tourism-events', 'major-events'],
                        updateFrequency: 'Daily',
                        reliability: 'High',
                        verified: true,
                        specialEvents: ['YMBL South Texas State Fair', 'The Gusher Marathon', 'Dogtober Fest']
                    },
                    {
                        name: 'Beaumont CVB Live Music Directory',
                        url: 'https://beaumontcvb.com/things-to-do/entertainment-nightlife/live-music/',
                        type: 'venue-directory',
                        priority: 9,
                        description: 'Directory of live music venues with links to individual venue calendars',
                        coverage: ['live-music', 'nightlife', 'entertainment-venues'],
                        updateFrequency: 'Weekly',
                        reliability: 'High',
                        verified: true
                    }
                ];

                const container = document.getElementById('tier1Sources');
                container.innerHTML = tier1Sources.map(source => this.createSourceCard(source)).join('');
            }

            renderTier2Sources() {
                const tier2Sources = [
                    {
                        name: 'Frances Ann Lutcher Theater',
                        url: 'https://www.lutcher.org',
                        type: 'theater-venue',
                        priority: 9,
                        description: '1,450-seat theater in Orange, TX with Broadway musicals, comedy shows, and performances',
                        coverage: ['broadway', 'theater', 'comedy', 'family-shows'],
                        capacity: '1,450 seats',
                        attendance: '30,000 annual',
                        verified: true
                    },
                    {
                        name: 'Symphony Orchestra of Southeast Texas',
                        url: 'https://www.sost.org',
                        type: 'symphony-orchestra',
                        priority: 9,
                        description: '67-year history orchestra with 70 musicians offering multiple concert series',
                        coverage: ['classical-music', 'symphony', 'educational-concerts'],
                        phone: '(*************',
                        verified: true
                    }
                ];

                const container = document.getElementById('tier2Sources');
                container.innerHTML = tier2Sources.map(source => this.createSourceCard(source)).join('');
            }

            renderCommunityContacts() {
                const communityContacts = [
                    {
                        name: 'Beaumont Parks and Recreation Department',
                        phone: '(*************',
                        email: '<EMAIL>',
                        type: 'community-contact',
                        priority: 8,
                        description: 'Direct contact for community center events, recreation programs, and facility rentals',
                        coverage: ['community-centers', 'recreation-programs', 'facility-rentals'],
                        note: 'Direct contact recommended as not all community center events are listed online',
                        venues: [
                            'Sterling Pruitt Activity Center (ext. 1206)',
                            'The Lakeside Center',
                            'Municipal Tennis Center'
                        ]
                    }
                ];

                const container = document.getElementById('communitySources');
                container.innerHTML = communityContacts.map(contact => this.createContactCard(contact)).join('');
            }

            createSourceCard(source) {
                const verifiedBadge = source.verified ? '<span class="verified-badge">✅ VERIFIED</span>' : '';
                
                return `
                    <div class="source-card">
                        <div class="source-header">
                            <div>
                                <div class="source-title">${source.name}${verifiedBadge}</div>
                                <div class="source-type">${source.type.replace('-', ' ')}</div>
                            </div>
                            <div class="source-priority">Priority ${source.priority}</div>
                        </div>
                        
                        <div class="source-description">${source.description}</div>
                        
                        <div class="source-details">
                            ${source.updateFrequency ? `
                            <div class="detail-item">
                                <div class="detail-label">Update Frequency</div>
                                <div class="detail-value">${source.updateFrequency}</div>
                            </div>
                            ` : ''}
                            ${source.reliability ? `
                            <div class="detail-item">
                                <div class="detail-label">Reliability</div>
                                <div class="detail-value">${source.reliability}</div>
                            </div>
                            ` : ''}
                            ${source.capacity ? `
                            <div class="detail-item">
                                <div class="detail-label">Capacity</div>
                                <div class="detail-value">${source.capacity}</div>
                            </div>
                            ` : ''}
                            ${source.phone ? `
                            <div class="detail-item">
                                <div class="detail-label">Phone</div>
                                <div class="detail-value">${source.phone}</div>
                            </div>
                            ` : ''}
                        </div>
                        
                        <div class="source-coverage">
                            <div class="coverage-title">Coverage Areas</div>
                            <div class="coverage-tags">
                                ${source.coverage.map(area => `<span class="coverage-tag">${area.replace('-', ' ')}</span>`).join('')}
                            </div>
                        </div>
                        
                        ${source.specialEvents ? `
                        <div class="source-coverage">
                            <div class="coverage-title">Special Events</div>
                            <div class="coverage-tags">
                                ${source.specialEvents.map(event => `<span class="coverage-tag">${event}</span>`).join('')}
                            </div>
                        </div>
                        ` : ''}
                        
                        <div class="source-actions">
                            <a href="${source.url}" target="_blank" class="btn btn-primary">Visit Source</a>
                            <button onclick="testScraping('${source.name}')" class="btn btn-outline">Test Scraping</button>
                        </div>
                    </div>
                `;
            }

            createContactCard(contact) {
                return `
                    <div class="source-card">
                        <div class="source-header">
                            <div>
                                <div class="source-title">${contact.name}</div>
                                <div class="source-type">${contact.type.replace('-', ' ')}</div>
                            </div>
                            <div class="source-priority">Priority ${contact.priority}</div>
                        </div>
                        
                        <div class="source-description">${contact.description}</div>
                        
                        <div class="source-details">
                            <div class="detail-item">
                                <div class="detail-label">Phone</div>
                                <div class="detail-value">${contact.phone}</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">Email</div>
                                <div class="detail-value">${contact.email}</div>
                            </div>
                        </div>
                        
                        <div class="source-coverage">
                            <div class="coverage-title">Services</div>
                            <div class="coverage-tags">
                                ${contact.coverage.map(service => `<span class="coverage-tag">${service.replace('-', ' ')}</span>`).join('')}
                            </div>
                        </div>
                        
                        ${contact.venues ? `
                        <div class="source-coverage">
                            <div class="coverage-title">Managed Venues</div>
                            <div class="coverage-tags">
                                ${contact.venues.map(venue => `<span class="coverage-tag">${venue}</span>`).join('')}
                            </div>
                        </div>
                        ` : ''}
                        
                        <div class="contact-info">
                            <div class="contact-title">📞 Contact Information</div>
                            <div class="contact-details">
                                <strong>Phone:</strong> ${contact.phone}<br>
                                <strong>Email:</strong> ${contact.email}<br>
                                ${contact.note ? `<strong>Note:</strong> ${contact.note}` : ''}
                            </div>
                        </div>
                        
                        <div class="source-actions">
                            <a href="tel:${contact.phone}" class="btn btn-primary">Call Now</a>
                            <a href="mailto:${contact.email}" class="btn btn-outline">Send Email</a>
                        </div>
                    </div>
                `;
            }
        }

        function testScraping(sourceName) {
            alert(`Testing scraping for: ${sourceName}\n\nThis would initiate a test scraping process for this specific source.`);
        }

        // Initialize the application
        document.addEventListener('DOMContentLoaded', () => {
            new DataSourcesPage();
        });
    </script>
</body>
</html>
