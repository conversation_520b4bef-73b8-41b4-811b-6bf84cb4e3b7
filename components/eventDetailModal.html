<!-- Event Detail Modal Component -->
<style>
.event-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.8);
    backdrop-filter: blur(5px);
    animation: fadeIn 0.3s ease;
}

.event-modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.event-modal-content {
    background: white;
    border-radius: 20px;
    width: 90%;
    max-width: 900px;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
    animation: slideIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { transform: translateY(-50px) scale(0.9); opacity: 0; }
    to { transform: translateY(0) scale(1); opacity: 1; }
}

.event-modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    border-radius: 20px 20px 0 0;
    position: relative;
}

.event-modal-close {
    position: absolute;
    top: 20px;
    right: 25px;
    font-size: 30px;
    font-weight: bold;
    cursor: pointer;
    color: white;
    background: rgba(255,255,255,0.2);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.event-modal-close:hover {
    background: rgba(255,255,255,0.3);
    transform: rotate(90deg);
}

.event-title-modal {
    font-size: 2.2em;
    font-weight: bold;
    margin-bottom: 15px;
    margin-right: 60px;
    line-height: 1.2;
}

.event-subtitle {
    font-size: 1.1em;
    opacity: 0.9;
    margin-bottom: 20px;
}

.event-quick-info {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-top: 20px;
}

.quick-info-item {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(255,255,255,0.2);
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.95em;
}

.event-modal-body {
    padding: 30px;
}

.event-section {
    margin-bottom: 30px;
}

.event-section h3 {
    font-size: 1.4em;
    color: #333;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.event-section-icon {
    font-size: 1.2em;
    width: 30px;
    text-align: center;
}

.event-details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.detail-card {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    border-left: 4px solid #667eea;
}

.detail-label {
    font-size: 0.85em;
    color: #666;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 8px;
}

.detail-value {
    font-size: 1.1em;
    color: #333;
    font-weight: 500;
}

.detail-value a {
    color: #667eea;
    text-decoration: none;
}

.detail-value a:hover {
    text-decoration: underline;
}

.venue-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 15px;
    padding: 25px;
    border: 1px solid #dee2e6;
}

.venue-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
}

.venue-icon {
    background: #667eea;
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5em;
}

.venue-info h4 {
    font-size: 1.3em;
    color: #333;
    margin-bottom: 5px;
}

.venue-type {
    color: #666;
    font-size: 0.9em;
    text-transform: capitalize;
}

.venue-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.venue-detail {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    background: white;
    border-radius: 8px;
}

.event-description-full {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    line-height: 1.7;
    color: #555;
    font-size: 1.05em;
}

.event-tags-modal {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 15px;
}

.tag-modal {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9em;
    font-weight: 500;
}

.event-actions-modal {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 25px;
    text-align: center;
}

.action-buttons-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 20px;
}

.btn-modal {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 12px 20px;
    border: none;
    border-radius: 10px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1em;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary-modal {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.btn-primary-modal:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.btn-outline-modal {
    background: transparent;
    color: #667eea;
    border: 2px solid #667eea;
}

.btn-outline-modal:hover {
    background: #667eea;
    color: white;
}

.btn-success-modal {
    background: #28a745;
    color: white;
}

.btn-success-modal:hover {
    background: #218838;
    transform: translateY(-2px);
}

.amenities-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
}

.amenity-tag {
    background: #e9ecef;
    color: #495057;
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 0.85em;
    display: flex;
    align-items: center;
    gap: 5px;
}

.source-info {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 15px;
    margin-top: 20px;
}

.source-info h4 {
    color: #856404;
    margin-bottom: 10px;
    font-size: 1.1em;
}

.source-link {
    color: #856404;
    text-decoration: none;
    font-weight: 500;
}

.source-link:hover {
    text-decoration: underline;
}

@media (max-width: 768px) {
    .event-modal-content {
        width: 95%;
        margin: 20px;
    }
    
    .event-title-modal {
        font-size: 1.8em;
    }
    
    .event-details-grid {
        grid-template-columns: 1fr;
    }
    
    .action-buttons-grid {
        grid-template-columns: 1fr;
    }
    
    .event-quick-info {
        flex-direction: column;
        gap: 10px;
    }
}
</style>

<div id="eventDetailModal" class="event-modal">
    <div class="event-modal-content">
        <div class="event-modal-header">
            <span class="event-modal-close">&times;</span>
            <h2 id="modalEventTitle" class="event-title-modal"></h2>
            <p id="modalEventSubtitle" class="event-subtitle"></p>
            <div id="modalQuickInfo" class="event-quick-info"></div>
        </div>
        
        <div class="event-modal-body">
            <!-- Event Details Section -->
            <div class="event-section">
                <h3><span class="event-section-icon">📋</span>Event Details</h3>
                <div id="modalEventDetails" class="event-details-grid"></div>
            </div>
            
            <!-- Description Section -->
            <div class="event-section">
                <h3><span class="event-section-icon">📝</span>Description</h3>
                <div id="modalEventDescription" class="event-description-full"></div>
                <div id="modalEventTags" class="event-tags-modal"></div>
            </div>
            
            <!-- Venue Section -->
            <div class="event-section">
                <h3><span class="event-section-icon">🏢</span>Venue Information</h3>
                <div id="modalVenueInfo" class="venue-card"></div>
            </div>
            
            <!-- Actions Section -->
            <div class="event-section">
                <h3><span class="event-section-icon">🎯</span>Event Actions</h3>
                <div class="event-actions-modal">
                    <p>Take action on this event:</p>
                    <div id="modalActionButtons" class="action-buttons-grid"></div>
                </div>
            </div>
            
            <!-- Source Information -->
            <div id="modalSourceInfo" class="source-info"></div>
        </div>
    </div>
</div>

<script>
class EventDetailModal {
    constructor() {
        this.modal = document.getElementById('eventDetailModal');
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Close modal when clicking X or outside
        const closeBtn = this.modal.querySelector('.event-modal-close');
        closeBtn.addEventListener('click', () => this.close());
        
        this.modal.addEventListener('click', (e) => {
            if (e.target === this.modal) {
                this.close();
            }
        });
        
        // Close on Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.modal.classList.contains('show')) {
                this.close();
            }
        });
    }

    show(eventData) {
        this.populateModal(eventData);
        this.modal.classList.add('show');
        document.body.style.overflow = 'hidden';
    }

    close() {
        this.modal.classList.remove('show');
        document.body.style.overflow = '';
    }

    populateModal(event) {
        // Header information
        document.getElementById('modalEventTitle').textContent = event.originalEvent?.title || event.title;
        document.getElementById('modalEventSubtitle').textContent = event.originalEvent?.organizer || event.organizer;
        
        // Quick info
        this.populateQuickInfo(event);
        
        // Event details
        this.populateEventDetails(event);
        
        // Description
        this.populateDescription(event);
        
        // Venue information
        this.populateVenueInfo(event);
        
        // Action buttons
        this.populateActionButtons(event);
        
        // Source information
        this.populateSourceInfo(event);
    }

    populateQuickInfo(event) {
        const quickInfo = document.getElementById('modalQuickInfo');
        const date = new Date(event.originalEvent?.date || event.date);
        const formattedDate = date.toLocaleDateString('en-US', {
            weekday: 'short',
            month: 'short',
            day: 'numeric'
        });
        const formattedTime = date.toLocaleTimeString('en-US', {
            hour: 'numeric',
            minute: '2-digit'
        });

        quickInfo.innerHTML = `
            <div class="quick-info-item">
                <span>📅</span>
                <span>${formattedDate}</span>
            </div>
            <div class="quick-info-item">
                <span>⏰</span>
                <span>${formattedTime}</span>
            </div>
            <div class="quick-info-item">
                <span>🏷️</span>
                <span>${event.originalEvent?.category || event.category}</span>
            </div>
            ${event.extractedPrice || event.price ? `
            <div class="quick-info-item">
                <span>💰</span>
                <span>${event.extractedPrice || event.price}</span>
            </div>
            ` : ''}
        `;
    }

    populateEventDetails(event) {
        const details = document.getElementById('modalEventDetails');
        const date = new Date(event.originalEvent?.date || event.date);
        
        details.innerHTML = `
            <div class="detail-card">
                <div class="detail-label">Date & Time</div>
                <div class="detail-value">${date.toLocaleDateString('en-US', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                })}<br>${date.toLocaleTimeString('en-US', {
                    hour: 'numeric',
                    minute: '2-digit'
                })}</div>
            </div>
            
            <div class="detail-card">
                <div class="detail-label">Organizer</div>
                <div class="detail-value">${event.originalEvent?.organizer || event.organizer}</div>
            </div>
            
            <div class="detail-card">
                <div class="detail-label">Category</div>
                <div class="detail-value">${event.originalEvent?.category || event.category}</div>
            </div>
            
            ${event.extractedPrice || event.price ? `
            <div class="detail-card">
                <div class="detail-label">Price</div>
                <div class="detail-value">${event.extractedPrice || event.price}</div>
            </div>
            ` : ''}
            
            ${event.extractedPhone || event.phone ? `
            <div class="detail-card">
                <div class="detail-label">Contact Phone</div>
                <div class="detail-value">
                    <a href="tel:${event.extractedPhone || event.phone}">${event.extractedPhone || event.phone}</a>
                </div>
            </div>
            ` : ''}
            
            ${event.extractedEmail || event.email ? `
            <div class="detail-card">
                <div class="detail-label">Contact Email</div>
                <div class="detail-value">
                    <a href="mailto:${event.extractedEmail || event.email}">${event.extractedEmail || event.email}</a>
                </div>
            </div>
            ` : ''}
        `;
    }

    populateDescription(event) {
        const description = document.getElementById('modalEventDescription');
        const tags = document.getElementById('modalEventTags');
        
        description.textContent = event.customDescription || event.originalEvent?.description || event.description;
        
        // Tags
        const eventTags = event.extractedTags || [];
        if (eventTags.length > 0) {
            tags.innerHTML = eventTags.map(tag => `<span class="tag-modal">${tag}</span>`).join('');
        } else {
            tags.innerHTML = '';
        }
    }

    populateVenueInfo(event) {
        const venueInfo = document.getElementById('modalVenueInfo');
        
        if (event.venue || event.venueName) {
            const venue = event.venue || {
                name: event.venueName,
                address: event.venueAddress,
                phone: event.venuePhone,
                website: event.venueWebsite,
                type: event.venueType,
                capacity: event.venueCapacity,
                amenities: event.venueAmenities
            };
            
            venueInfo.innerHTML = `
                <div class="venue-header">
                    <div class="venue-icon">🏢</div>
                    <div class="venue-info">
                        <h4>${venue.name}</h4>
                        <div class="venue-type">${venue.type?.replace('-', ' ') || 'Venue'}</div>
                    </div>
                </div>
                
                <div class="venue-details">
                    <div class="venue-detail">
                        <span>📍</span>
                        <div>
                            <div class="detail-label">Address</div>
                            <div class="detail-value">${venue.address || event.originalEvent?.location || event.location}</div>
                        </div>
                    </div>
                    
                    ${venue.phone ? `
                    <div class="venue-detail">
                        <span>📞</span>
                        <div>
                            <div class="detail-label">Phone</div>
                            <div class="detail-value">
                                <a href="tel:${venue.phone}">${venue.phone}</a>
                            </div>
                        </div>
                    </div>
                    ` : ''}
                    
                    ${venue.website ? `
                    <div class="venue-detail">
                        <span>🌐</span>
                        <div>
                            <div class="detail-label">Website</div>
                            <div class="detail-value">
                                <a href="${venue.website}" target="_blank">Visit Website</a>
                            </div>
                        </div>
                    </div>
                    ` : ''}
                    
                    ${venue.capacity ? `
                    <div class="venue-detail">
                        <span>👥</span>
                        <div>
                            <div class="detail-label">Capacity</div>
                            <div class="detail-value">${venue.capacity} people</div>
                        </div>
                    </div>
                    ` : ''}
                </div>
                
                ${venue.amenities && venue.amenities.length > 0 ? `
                <div style="margin-top: 15px;">
                    <div class="detail-label">Amenities</div>
                    <div class="amenities-list">
                        ${venue.amenities.map(amenity => `
                            <span class="amenity-tag">
                                ${this.getAmenityIcon(amenity)} ${amenity.replace('-', ' ')}
                            </span>
                        `).join('')}
                    </div>
                </div>
                ` : ''}
            `;
        } else {
            venueInfo.innerHTML = `
                <div class="venue-detail">
                    <span>📍</span>
                    <div>
                        <div class="detail-label">Location</div>
                        <div class="detail-value">${event.originalEvent?.location || event.location}</div>
                    </div>
                </div>
            `;
        }
    }

    populateActionButtons(event) {
        const buttons = document.getElementById('modalActionButtons');
        const originalUrl = event.originalEvent?.url || event.url;
        
        buttons.innerHTML = `
            ${originalUrl ? `
            <a href="${originalUrl}" target="_blank" class="btn-modal btn-primary-modal">
                <span>🔗</span>
                <span>View Original Event</span>
            </a>
            ` : ''}
            
            ${event.venue?.id ? `
            <a href="/venue.html?id=${event.venue.id}" class="btn-modal btn-outline-modal">
                <span>🏢</span>
                <span>View Venue Page</span>
            </a>
            ` : ''}
            
            <button onclick="shareEvent('${event.id || 'event'}')" class="btn-modal btn-outline-modal">
                <span>📤</span>
                <span>Share Event</span>
            </button>
            
            <button onclick="addToCalendar('${event.id || 'event'}')" class="btn-modal btn-outline-modal">
                <span>📅</span>
                <span>Add to Calendar</span>
            </button>
            
            ${event.extractedPhone || event.phone ? `
            <a href="tel:${event.extractedPhone || event.phone}" class="btn-modal btn-success-modal">
                <span>📞</span>
                <span>Call ${event.extractedPhone || event.phone}</span>
            </a>
            ` : ''}
            
            ${event.extractedEmail || event.email ? `
            <a href="mailto:${event.extractedEmail || event.email}" class="btn-modal btn-outline-modal">
                <span>✉️</span>
                <span>Email Organizer</span>
            </a>
            ` : ''}
        `;
    }

    populateSourceInfo(event) {
        const sourceInfo = document.getElementById('modalSourceInfo');
        const scrapingSource = event.scrapingSource || event.originalEvent?.url || event.url;
        
        if (scrapingSource) {
            sourceInfo.innerHTML = `
                <h4>📊 Data Source</h4>
                <p>This event information was scraped from: 
                    <a href="${scrapingSource}" target="_blank" class="source-link">${scrapingSource}</a>
                </p>
                ${event.scrapingType ? `<p><strong>Source Type:</strong> ${event.scrapingType.replace('-', ' ')}</p>` : ''}
                ${event.lastEnhanced ? `<p><strong>Last Updated:</strong> ${new Date(event.lastEnhanced).toLocaleString()}</p>` : ''}
            `;
        } else {
            sourceInfo.style.display = 'none';
        }
    }

    getAmenityIcon(amenity) {
        const icons = {
            'parking': '🚗',
            'wheelchair-accessible': '♿',
            'concessions': '🍿',
            'wifi': '📶',
            'air-conditioning': '❄️',
            'outdoor': '🌳',
            'family-friendly': '👨‍👩‍👧‍👦',
            'educational': '📚',
            'food-service': '🍽️',
            'gift-shop': '🛍️'
        };
        return icons[amenity] || '✨';
    }
}

// Initialize modal when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.eventDetailModal = new EventDetailModal();
});

// Global functions for event actions
function shareEvent(eventId) {
    if (navigator.share) {
        navigator.share({
            title: document.getElementById('modalEventTitle').textContent,
            url: window.location.href
        });
    } else {
        navigator.clipboard.writeText(window.location.href).then(() => {
            alert('Event link copied to clipboard!');
        });
    }
}

function addToCalendar(eventId) {
    alert('Calendar integration coming soon!');
}
</script>
