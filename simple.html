<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Southeast Texas Events - Simple Version</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        .controls {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
            align-items: center;
        }
        input, select, button {
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
        }
        input, select {
            flex: 1;
            min-width: 200px;
        }
        button {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border: none;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.4);
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: bold;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.loading { background: #d1ecf1; color: #0c5460; }
        .events-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        .event-card {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        .event-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .event-category {
            position: absolute;
            top: 15px;
            right: 15px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: bold;
            text-transform: uppercase;
        }
        .event-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
            margin-right: 80px;
            line-height: 1.3;
        }
        .event-date {
            color: #667eea;
            font-weight: bold;
            margin-bottom: 8px;
            font-size: 1.1em;
        }
        .event-location {
            color: #666;
            margin-bottom: 10px;
            font-size: 0.95em;
        }
        .event-organizer {
            color: #667eea;
            font-weight: bold;
            font-size: 0.9em;
            margin-bottom: 10px;
        }
        .event-description {
            color: #555;
            line-height: 1.5;
            margin-bottom: 15px;
        }
        .event-link {
            display: inline-block;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .event-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
            color: white;
            text-decoration: none;
        }
        .no-events {
            text-align: center;
            color: #666;
            font-size: 1.2em;
            padding: 40px;
            background: #f8f9fa;
            border-radius: 8px;
            margin: 20px 0;
        }
        .event-count {
            text-align: center;
            color: #666;
            margin-bottom: 20px;
            font-size: 1.1em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌟 Southeast Texas Events</h1>
        
        <div class="controls">
            <input type="text" id="searchBox" placeholder="Search events...">
            <select id="categoryFilter">
                <option value="">All Categories</option>
                <option value="business">Business</option>
                <option value="entertainment">Entertainment</option>
                <option value="education">Education</option>
                <option value="sports">Sports</option>
                <option value="community">Community</option>
                <option value="arts">Arts & Culture</option>
                <option value="food">Food & Dining</option>
                <option value="health">Health & Wellness</option>
            </select>
            <button id="loadBtn">Load Events</button>
            <button id="scrapeBtn">Refresh Events</button>
        </div>

        <div id="status" class="status" style="display: none;"></div>
        <div id="eventCount" class="event-count"></div>
        <div id="eventsGrid" class="events-grid"></div>
    </div>

    <script>
        class SimpleEventViewer {
            constructor() {
                this.events = [];
                this.filteredEvents = [];
                this.apiBaseUrl = window.location.origin;
                this.init();
            }

            init() {
                this.setupEventListeners();
                this.loadEvents();
            }

            setupEventListeners() {
                document.getElementById('searchBox').addEventListener('input', () => this.filterEvents());
                document.getElementById('categoryFilter').addEventListener('change', () => this.filterEvents());
                document.getElementById('loadBtn').addEventListener('click', () => this.loadEvents());
                document.getElementById('scrapeBtn').addEventListener('click', () => this.triggerScraping());
            }

            showStatus(message, type = 'info') {
                const status = document.getElementById('status');
                status.className = `status ${type}`;
                status.textContent = message;
                status.style.display = 'block';
                console.log(`[${type.toUpperCase()}] ${message}`);
            }

            async loadEvents() {
                try {
                    this.showStatus('Loading events from API...', 'loading');
                    console.log('🔄 Loading events...');
                    
                    const response = await fetch(`${this.apiBaseUrl}/api/events`);
                    console.log('Response status:', response.status);
                    
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    
                    const data = await response.json();
                    console.log('API Response:', data);
                    
                    if (data.success && data.events) {
                        this.events = data.events;
                        this.filteredEvents = [...this.events];
                        this.renderEvents();
                        this.showStatus(`✅ Loaded ${this.events.length} events successfully`, 'success');
                        
                        setTimeout(() => {
                            document.getElementById('status').style.display = 'none';
                        }, 3000);
                    } else {
                        throw new Error('No events found in API response');
                    }
                    
                } catch (error) {
                    console.error('Load error:', error);
                    this.showStatus(`❌ Error: ${error.message}`, 'error');
                }
            }

            async triggerScraping() {
                try {
                    this.showStatus('Starting web scraping...', 'loading');
                    
                    const response = await fetch(`${this.apiBaseUrl}/api/events/scrape`, {
                        method: 'POST'
                    });
                    const data = await response.json();
                    
                    if (data.success) {
                        this.showStatus('✅ Scraping started! Reloading in 5 seconds...', 'success');
                        setTimeout(() => {
                            this.loadEvents();
                        }, 5000);
                    } else {
                        throw new Error(data.error || 'Failed to start scraping');
                    }
                    
                } catch (error) {
                    console.error('Scraping error:', error);
                    this.showStatus(`❌ Scraping Error: ${error.message}`, 'error');
                }
            }

            filterEvents() {
                const searchTerm = document.getElementById('searchBox').value.toLowerCase();
                const categoryFilter = document.getElementById('categoryFilter').value;

                this.filteredEvents = this.events.filter(event => {
                    const matchesSearch = !searchTerm || 
                        event.title.toLowerCase().includes(searchTerm) ||
                        event.description.toLowerCase().includes(searchTerm) ||
                        event.location.toLowerCase().includes(searchTerm);
                    
                    const matchesCategory = !categoryFilter || event.category === categoryFilter;
                    
                    return matchesSearch && matchesCategory;
                });

                this.renderEvents();
            }

            renderEvents() {
                console.log('🎨 Rendering events...');
                const grid = document.getElementById('eventsGrid');
                const countDiv = document.getElementById('eventCount');
                
                countDiv.textContent = `Showing ${this.filteredEvents.length} of ${this.events.length} events`;
                
                if (this.filteredEvents.length === 0) {
                    grid.innerHTML = '<div class="no-events">No events found matching your criteria.</div>';
                    return;
                }

                grid.innerHTML = this.filteredEvents.map(event => this.createEventCard(event)).join('');
                console.log(`✅ Rendered ${this.filteredEvents.length} events`);
            }

            createEventCard(event) {
                const date = new Date(event.date);
                const formattedDate = date.toLocaleDateString('en-US', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                });

                return `
                    <div class="event-card">
                        <div class="event-category">${event.category}</div>
                        <h3 class="event-title">${event.title}</h3>
                        <div class="event-date">📅 ${formattedDate}</div>
                        <div class="event-location">📍 ${event.location}</div>
                        <div class="event-organizer">🏢 ${event.organizer}</div>
                        <div class="event-description">${event.description}</div>
                        <a href="${event.url}" target="_blank" class="event-link">View Event Details →</a>
                    </div>
                `;
            }
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🚀 Simple Event Viewer starting...');
            new SimpleEventViewer();
        });
    </script>
</body>
</html>
