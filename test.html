<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Connection Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-box {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .success { border-left: 4px solid #28a745; }
        .error { border-left: 4px solid #dc3545; }
        .loading { border-left: 4px solid #007bff; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>🔧 Southeast Texas Events - Connection Test</h1>
    
    <div class="test-box">
        <h2>📡 Server Connection Test</h2>
        <p>This page will test if we can connect to the localhost:3000 server.</p>
        <button onclick="testConnection()">Test Connection</button>
        <button onclick="loadEvents()">Load Events</button>
        <div id="connectionResult"></div>
    </div>

    <div class="test-box">
        <h2>📊 API Response</h2>
        <div id="apiResponse"></div>
    </div>

    <div class="test-box">
        <h2>🎯 Quick Access Links</h2>
        <p>Try these direct links:</p>
        <ul>
            <li><a href="http://localhost:3000" target="_blank">Main Application</a></li>
            <li><a href="http://localhost:3000/simple.html" target="_blank">Simple Version</a></li>
            <li><a href="http://localhost:3000/debug.html" target="_blank">Debug Version</a></li>
            <li><a href="http://localhost:3000/api/test" target="_blank">API Test</a></li>
            <li><a href="http://localhost:3000/api/events" target="_blank">Events API</a></li>
        </ul>
    </div>

    <script>
        async function testConnection() {
            const resultDiv = document.getElementById('connectionResult');
            resultDiv.innerHTML = '<div class="loading">🔄 Testing connection...</div>';
            
            try {
                console.log('Testing connection to localhost:3000...');
                
                const response = await fetch('http://localhost:3000/api/test');
                console.log('Response received:', response);
                
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.innerHTML = `
                        <div class="success">
                            ✅ <strong>Connection Successful!</strong><br>
                            Server is running and responding.<br>
                            Response: ${data.message}
                        </div>
                    `;
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
            } catch (error) {
                console.error('Connection test failed:', error);
                resultDiv.innerHTML = `
                    <div class="error">
                        ❌ <strong>Connection Failed!</strong><br>
                        Error: ${error.message}<br><br>
                        <strong>Possible solutions:</strong><br>
                        • Check if server is running<br>
                        • Try refreshing the page<br>
                        • Clear browser cache<br>
                        • Try a different browser<br>
                        • Check firewall settings
                    </div>
                `;
            }
        }

        async function loadEvents() {
            const responseDiv = document.getElementById('apiResponse');
            responseDiv.innerHTML = '<div class="loading">🔄 Loading events...</div>';
            
            try {
                const response = await fetch('http://localhost:3000/api/events');
                const data = await response.json();
                
                responseDiv.innerHTML = `
                    <div class="success">
                        ✅ <strong>Events Loaded Successfully!</strong><br>
                        Found ${data.count} events<br><br>
                        <strong>Sample Event:</strong><br>
                        <pre>${JSON.stringify(data.events[0], null, 2)}</pre>
                    </div>
                `;
                
            } catch (error) {
                console.error('Load events failed:', error);
                responseDiv.innerHTML = `
                    <div class="error">
                        ❌ <strong>Failed to Load Events!</strong><br>
                        Error: ${error.message}
                    </div>
                `;
            }
        }

        // Auto-test on page load
        window.addEventListener('load', () => {
            console.log('🔧 Test page loaded');
            setTimeout(testConnection, 1000);
        });
    </script>
</body>
</html>
