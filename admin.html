<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Southeast Texas Events - Admin Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f7fa;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            font-size: 1.8em;
            font-weight: 600;
        }
        
        .header-actions {
            display: flex;
            gap: 15px;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #1e7e34;
        }
        
        .btn-outline {
            background: transparent;
            color: white;
            border: 2px solid white;
        }
        
        .btn-outline:hover {
            background: white;
            color: #667eea;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 30px 20px;
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .stats-panel {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        }
        
        .stats-panel h2 {
            margin-bottom: 20px;
            color: #333;
            font-size: 1.4em;
        }
        
        .stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #eee;
        }
        
        .stat-item:last-child {
            border-bottom: none;
        }
        
        .stat-label {
            font-weight: 500;
            color: #666;
        }
        
        .stat-value {
            font-weight: bold;
            color: #333;
            font-size: 1.1em;
        }
        
        .actions-panel {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        }
        
        .action-item {
            margin-bottom: 15px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #f8f9fa;
        }
        
        .action-item h3 {
            margin-bottom: 8px;
            color: #333;
        }
        
        .action-item p {
            color: #666;
            margin-bottom: 10px;
            font-size: 0.9em;
        }
        
        .tabs {
            display: flex;
            background: white;
            border-radius: 12px 12px 0 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            margin-bottom: 0;
        }
        
        .tab {
            padding: 15px 25px;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .tab.active {
            border-bottom-color: #667eea;
            color: #667eea;
            background: #f8f9ff;
        }
        
        .tab:hover {
            background: #f5f5f5;
        }
        
        .tab-content {
            background: white;
            border-radius: 0 0 12px 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            min-height: 500px;
        }
        
        .tab-pane {
            display: none;
            padding: 25px;
        }
        
        .tab-pane.active {
            display: block;
        }
        
        .event-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
        }
        
        .event-card {
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            padding: 20px;
            background: white;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .event-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        
        .event-status {
            position: absolute;
            top: 15px;
            right: 15px;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
        }
        
        .status-verified {
            background: #d4edda;
            color: #155724;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .event-title {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 10px;
            margin-right: 80px;
            color: #333;
        }
        
        .event-meta {
            color: #666;
            font-size: 0.9em;
            margin-bottom: 8px;
        }
        
        .event-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            margin: 10px 0;
        }
        
        .tag {
            background: #e9ecef;
            color: #495057;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 0.8em;
        }
        
        .event-actions {
            margin-top: 15px;
            display: flex;
            gap: 10px;
        }
        
        .btn-sm {
            padding: 6px 12px;
            font-size: 0.85em;
        }
        
        .btn-edit {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-edit:hover {
            background: #e0a800;
        }
        
        .btn-verify {
            background: #28a745;
            color: white;
        }
        
        .btn-verify:hover {
            background: #1e7e34;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .status-message {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }
        
        .status-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .venue-list {
            display: grid;
            gap: 15px;
        }
        
        .venue-item {
            background: #f8f9fa;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .venue-info h4 {
            margin-bottom: 5px;
            color: #333;
        }
        
        .venue-info p {
            color: #666;
            font-size: 0.9em;
        }
        
        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .event-grid {
                grid-template-columns: 1fr;
            }
            
            .tabs {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <h1>🎯 Southeast Texas Events - Admin Dashboard</h1>
            <div class="header-actions">
                <a href="/" class="btn btn-outline">View Public Site</a>
                <button id="enhanceBtn" class="btn btn-success">Enhance Events</button>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="dashboard-grid">
            <div class="stats-panel">
                <h2>📊 System Statistics</h2>
                <div id="statsContent">
                    <div class="loading">Loading statistics...</div>
                </div>
            </div>
            
            <div class="actions-panel">
                <h2>🚀 Quick Actions</h2>
                <div class="action-item">
                    <h3>Enhance Events</h3>
                    <p>Process original events and extract additional data like venues, addresses, and contact info.</p>
                    <button id="enhanceEventsBtn" class="btn btn-primary btn-sm">Run Enhancement</button>
                </div>
                
                <div class="action-item">
                    <h3>Refresh Original Data</h3>
                    <p>Trigger new web scraping to get fresh events from Southeast Texas sources.</p>
                    <button id="refreshDataBtn" class="btn btn-primary btn-sm">Refresh Data</button>
                </div>
                
                <div class="action-item">
                    <h3>Export Data</h3>
                    <p>Download enhanced events and venue data as JSON files.</p>
                    <button id="exportDataBtn" class="btn btn-primary btn-sm">Export Data</button>
                </div>

                <div class="action-item">
                    <h3>Scrape Priority Venues</h3>
                    <p>Run comprehensive scraping of priority venues and data sources.</p>
                    <button id="scrapePriorityBtn" class="btn btn-success btn-sm">Scrape Venues</button>
                </div>

                <div class="action-item">
                    <h3>View Data Sources</h3>
                    <p>Review comprehensive data sources and scraping strategies.</p>
                    <button id="viewSourcesBtn" class="btn btn-info btn-sm">View Sources</button>
                </div>
            </div>
        </div>

        <div id="statusMessage"></div>

        <div class="tabs">
            <div class="tab active" data-tab="events">Enhanced Events</div>
            <div class="tab" data-tab="venues">Venues</div>
            <div class="tab" data-tab="original">Original Events</div>
        </div>

        <div class="tab-content">
            <div id="events-tab" class="tab-pane active">
                <div id="eventsContent">
                    <div class="loading">Loading enhanced events...</div>
                </div>
            </div>

            <div id="venues-tab" class="tab-pane">
                <div id="venuesContent">
                    <div class="loading">Loading venues...</div>
                </div>
            </div>

            <div id="original-tab" class="tab-pane">
                <div id="originalContent">
                    <div class="loading">Loading original events...</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        class AdminDashboard {
            constructor() {
                this.apiBaseUrl = window.location.origin;
                this.init();
            }

            init() {
                this.setupEventListeners();
                this.loadStats();
                this.loadEnhancedEvents();
                this.loadVenues();
                this.loadOriginalEvents();
            }

            setupEventListeners() {
                // Tab switching
                document.querySelectorAll('.tab').forEach(tab => {
                    tab.addEventListener('click', () => this.switchTab(tab.dataset.tab));
                });

                // Action buttons
                document.getElementById('enhanceBtn').addEventListener('click', () => this.enhanceEvents());
                document.getElementById('enhanceEventsBtn').addEventListener('click', () => this.enhanceEvents());
                document.getElementById('refreshDataBtn').addEventListener('click', () => this.refreshData());
                document.getElementById('exportDataBtn').addEventListener('click', () => this.exportData());
            }

            switchTab(tabName) {
                // Update tab buttons
                document.querySelectorAll('.tab').forEach(tab => {
                    tab.classList.remove('active');
                });
                document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

                // Update tab content
                document.querySelectorAll('.tab-pane').forEach(pane => {
                    pane.classList.remove('active');
                });
                document.getElementById(`${tabName}-tab`).classList.add('active');
            }

            showStatus(message, type = 'success') {
                const statusDiv = document.getElementById('statusMessage');
                statusDiv.className = `status-message status-${type}`;
                statusDiv.textContent = message;
                statusDiv.style.display = 'block';

                setTimeout(() => {
                    statusDiv.style.display = 'none';
                }, 5000);
            }

            async loadStats() {
                try {
                    const response = await fetch(`${this.apiBaseUrl}/api/enhanced/stats`);
                    const data = await response.json();

                    if (data.success) {
                        this.renderStats(data.stats);
                    } else {
                        throw new Error(data.error);
                    }
                } catch (error) {
                    console.error('Error loading stats:', error);
                    document.getElementById('statsContent').innerHTML = 
                        '<div class="status-error">Error loading statistics</div>';
                }
            }

            renderStats(stats) {
                const html = `
                    <div class="stat-item">
                        <span class="stat-label">Original Events</span>
                        <span class="stat-value">${stats.originalEvents}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Enhanced Events</span>
                        <span class="stat-value">${stats.enhancedEvents}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Verified Events</span>
                        <span class="stat-value">${stats.verifiedEvents}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Total Venues</span>
                        <span class="stat-value">${stats.totalVenues}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Verified Venues</span>
                        <span class="stat-value">${stats.verifiedVenues}</span>
                    </div>
                `;
                document.getElementById('statsContent').innerHTML = html;
            }

            async loadEnhancedEvents() {
                try {
                    const response = await fetch(`${this.apiBaseUrl}/api/enhanced/events`);
                    const data = await response.json();

                    if (data.success) {
                        this.renderEnhancedEvents(data.events);
                    } else {
                        throw new Error(data.error);
                    }
                } catch (error) {
                    console.error('Error loading enhanced events:', error);
                    document.getElementById('eventsContent').innerHTML = 
                        '<div class="status-error">Error loading enhanced events</div>';
                }
            }

            renderEnhancedEvents(events) {
                if (events.length === 0) {
                    document.getElementById('eventsContent').innerHTML = 
                        '<div class="status-message">No enhanced events found. Click "Enhance Events" to process original events.</div>';
                    return;
                }

                const html = `
                    <div class="event-grid">
                        ${events.map(event => this.createEnhancedEventCard(event)).join('')}
                    </div>
                `;
                document.getElementById('eventsContent').innerHTML = html;
            }

            createEnhancedEventCard(event) {
                const date = new Date(event.originalEvent.date);
                const formattedDate = date.toLocaleDateString('en-US', {
                    weekday: 'short',
                    month: 'short',
                    day: 'numeric'
                });

                return `
                    <div class="event-card">
                        <div class="event-status ${event.isVerified ? 'status-verified' : 'status-pending'}">
                            ${event.isVerified ? 'Verified' : 'Pending'}
                        </div>
                        <div class="event-title">${event.originalEvent.title}</div>
                        <div class="event-meta">📅 ${formattedDate}</div>
                        <div class="event-meta">📍 ${event.extractedVenueName || event.originalEvent.location}</div>
                        ${event.extractedPhone ? `<div class="event-meta">📞 ${event.extractedPhone}</div>` : ''}
                        ${event.extractedEmail ? `<div class="event-meta">✉️ ${event.extractedEmail}</div>` : ''}
                        ${event.extractedPrice ? `<div class="event-meta">💰 ${event.extractedPrice}</div>` : ''}
                        
                        <div class="event-tags">
                            ${event.extractedTags.map(tag => `<span class="tag">${tag}</span>`).join('')}
                        </div>
                        
                        <div class="event-actions">
                            <button class="btn btn-edit btn-sm" onclick="editEvent('${event.id}')">Edit</button>
                            ${!event.isVerified ? `<button class="btn btn-verify btn-sm" onclick="verifyEvent('${event.id}')">Verify</button>` : ''}
                        </div>
                    </div>
                `;
            }

            async loadVenues() {
                try {
                    const response = await fetch(`${this.apiBaseUrl}/api/enhanced/venues`);
                    const data = await response.json();

                    if (data.success) {
                        this.renderVenues(data.venues);
                    } else {
                        throw new Error(data.error);
                    }
                } catch (error) {
                    console.error('Error loading venues:', error);
                    document.getElementById('venuesContent').innerHTML = 
                        '<div class="status-error">Error loading venues</div>';
                }
            }

            renderVenues(venues) {
                if (venues.length === 0) {
                    document.getElementById('venuesContent').innerHTML = 
                        '<div class="status-message">No venues found. Enhance events to extract venue information.</div>';
                    return;
                }

                const html = `
                    <div class="venue-list">
                        ${venues.map(venue => this.createVenueItem(venue)).join('')}
                    </div>
                `;
                document.getElementById('venuesContent').innerHTML = html;
            }

            createVenueItem(venue) {
                return `
                    <div class="venue-item">
                        <div class="venue-info">
                            <h4>${venue.name}</h4>
                            <p>${venue.address}</p>
                            ${venue.phone ? `<p>📞 ${venue.phone}</p>` : ''}
                        </div>
                        <div class="venue-actions">
                            <span class="event-status ${venue.isVerified ? 'status-verified' : 'status-pending'}">
                                ${venue.isVerified ? 'Verified' : 'Pending'}
                            </span>
                            <button class="btn btn-edit btn-sm" onclick="editVenue('${venue.id}')">Edit</button>
                        </div>
                    </div>
                `;
            }

            async loadOriginalEvents() {
                try {
                    const response = await fetch(`${this.apiBaseUrl}/api/events`);
                    const data = await response.json();

                    if (data.success) {
                        this.renderOriginalEvents(data.events);
                    } else {
                        throw new Error(data.error);
                    }
                } catch (error) {
                    console.error('Error loading original events:', error);
                    document.getElementById('originalContent').innerHTML = 
                        '<div class="status-error">Error loading original events</div>';
                }
            }

            renderOriginalEvents(events) {
                const html = `
                    <div class="event-grid">
                        ${events.map(event => this.createOriginalEventCard(event)).join('')}
                    </div>
                `;
                document.getElementById('originalContent').innerHTML = html;
            }

            createOriginalEventCard(event) {
                const date = new Date(event.date);
                const formattedDate = date.toLocaleDateString('en-US', {
                    weekday: 'short',
                    month: 'short',
                    day: 'numeric'
                });

                return `
                    <div class="event-card">
                        <div class="event-title">${event.title}</div>
                        <div class="event-meta">📅 ${formattedDate}</div>
                        <div class="event-meta">📍 ${event.location}</div>
                        <div class="event-meta">🏢 ${event.organizer}</div>
                        <div class="event-meta">🔗 <a href="${event.url}" target="_blank">View Original</a></div>
                        <p style="margin-top: 10px; color: #666; font-size: 0.9em;">${event.description}</p>
                    </div>
                `;
            }

            async enhanceEvents() {
                try {
                    this.showStatus('Enhancing events...', 'success');
                    
                    const response = await fetch(`${this.apiBaseUrl}/api/enhanced/enhance`, {
                        method: 'POST'
                    });
                    const data = await response.json();

                    if (data.success) {
                        this.showStatus(`Successfully enhanced ${data.count} events!`, 'success');
                        this.loadStats();
                        this.loadEnhancedEvents();
                        this.loadVenues();
                    } else {
                        throw new Error(data.error);
                    }
                } catch (error) {
                    console.error('Error enhancing events:', error);
                    this.showStatus('Error enhancing events: ' + error.message, 'error');
                }
            }

            async refreshData() {
                try {
                    this.showStatus('Refreshing original data...', 'success');
                    
                    const response = await fetch(`${this.apiBaseUrl}/api/events/scrape`, {
                        method: 'POST'
                    });
                    const data = await response.json();

                    if (data.success) {
                        this.showStatus('Data refresh started! Check back in a few minutes.', 'success');
                        setTimeout(() => {
                            this.loadOriginalEvents();
                            this.loadStats();
                        }, 5000);
                    } else {
                        throw new Error(data.error);
                    }
                } catch (error) {
                    console.error('Error refreshing data:', error);
                    this.showStatus('Error refreshing data: ' + error.message, 'error');
                }
            }

            async exportData() {
                try {
                    // Export enhanced events
                    const eventsResponse = await fetch(`${this.apiBaseUrl}/api/enhanced/events`);
                    const eventsData = await eventsResponse.json();
                    
                    // Export venues
                    const venuesResponse = await fetch(`${this.apiBaseUrl}/api/enhanced/venues`);
                    const venuesData = await venuesResponse.json();

                    // Create download links
                    this.downloadJSON(eventsData.events, 'enhanced-events.json');
                    this.downloadJSON(venuesData.venues, 'venues.json');
                    
                    this.showStatus('Data exported successfully!', 'success');
                } catch (error) {
                    console.error('Error exporting data:', error);
                    this.showStatus('Error exporting data: ' + error.message, 'error');
                }
            }

            downloadJSON(data, filename) {
                const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = filename;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            }
        }

        // Global functions for event handlers
        function editEvent(eventId) {
            // TODO: Implement event editing modal
            alert(`Edit event: ${eventId}`);
        }

        function verifyEvent(eventId) {
            // TODO: Implement event verification
            alert(`Verify event: ${eventId}`);
        }

        function editVenue(venueId) {
            // TODO: Implement venue editing modal
            alert(`Edit venue: ${venueId}`);
        }

        // Initialize dashboard when page loads
        document.addEventListener('DOMContentLoaded', () => {
            new AdminDashboard();
        });
    </script>
</body>
</html>
