<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Venue Details - Southeast Texas Events</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            font-size: 1.8em;
            font-weight: 600;
        }
        
        .nav-links {
            display: flex;
            gap: 20px;
        }
        
        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 6px;
            transition: background 0.3s ease;
        }
        
        .nav-links a:hover {
            background: rgba(255,255,255,0.2);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px 20px;
        }
        
        .venue-hero {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }
        
        .venue-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .venue-header {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: 30px;
            align-items: start;
        }
        
        .venue-info {
            flex: 1;
        }
        
        .venue-name {
            font-size: 3em;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            line-height: 1.1;
        }
        
        .venue-type {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 8px 20px;
            border-radius: 25px;
            font-size: 1em;
            font-weight: 600;
            display: inline-block;
            margin-bottom: 20px;
            text-transform: capitalize;
        }
        
        .venue-stats {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            min-width: 250px;
        }
        
        .stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #dee2e6;
        }
        
        .stat-item:last-child {
            border-bottom: none;
        }
        
        .stat-label {
            color: #666;
            font-weight: 500;
        }
        
        .stat-value {
            font-weight: bold;
            color: #333;
        }
        
        .venue-details-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .detail-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        }
        
        .detail-section h3 {
            font-size: 1.4em;
            color: #333;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .section-icon {
            font-size: 1.3em;
            width: 35px;
            text-align: center;
        }
        
        .contact-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .contact-item:last-child {
            border-bottom: none;
        }
        
        .contact-icon {
            background: #667eea;
            color: white;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.9em;
        }
        
        .contact-info a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }
        
        .contact-info a:hover {
            text-decoration: underline;
        }
        
        .amenities-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
        }
        
        .amenity-item {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 12px;
            text-align: center;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
        }
        
        .amenity-icon {
            font-size: 1.5em;
        }
        
        .amenity-name {
            font-size: 0.9em;
            font-weight: 500;
            text-transform: capitalize;
        }
        
        .events-section {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        }
        
        .events-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
        }
        
        .events-header h2 {
            font-size: 1.8em;
            color: #333;
        }
        
        .events-filter {
            display: flex;
            gap: 15px;
            align-items: center;
        }
        
        .events-filter select {
            padding: 8px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 0.9em;
        }
        
        .events-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
        }
        
        .event-card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .event-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            background: white;
        }
        
        .event-date {
            background: #667eea;
            color: white;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 0.85em;
            font-weight: 600;
            display: inline-block;
            margin-bottom: 10px;
        }
        
        .event-title {
            font-size: 1.2em;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
            line-height: 1.3;
        }
        
        .event-description {
            color: #666;
            font-size: 0.95em;
            line-height: 1.5;
            margin-bottom: 15px;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .event-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.9em;
            color: #666;
        }
        
        .event-category {
            background: #e9ecef;
            color: #495057;
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 0.8em;
        }
        
        .loading {
            text-align: center;
            padding: 60px;
            color: #666;
            font-size: 1.2em;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin: 20px 0;
        }
        
        .no-events {
            text-align: center;
            padding: 40px;
            color: #666;
            background: #f8f9fa;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        @media (max-width: 768px) {
            .venue-name {
                font-size: 2.2em;
            }
            
            .venue-header {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .venue-details-grid {
                grid-template-columns: 1fr;
            }
            
            .events-header {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }
            
            .events-filter {
                justify-content: center;
            }
            
            .events-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <h1>🎯 Southeast Texas Events</h1>
            <div class="nav-links">
                <a href="/">Home</a>
                <a href="/admin.html">Admin</a>
                <a href="#share">Share</a>
            </div>
        </div>
    </div>

    <div class="container">
        <div id="venueContent">
            <div class="loading">Loading venue details...</div>
        </div>
    </div>

    <!-- Include Event Detail Modal -->
    <div id="eventModalContainer"></div>

    <script>
        class VenuePage {
            constructor() {
                this.apiBaseUrl = window.location.origin;
                this.venueId = this.getVenueIdFromUrl();
                this.venue = null;
                this.events = [];
                this.filteredEvents = [];
                this.init();
            }

            getVenueIdFromUrl() {
                const urlParams = new URLSearchParams(window.location.search);
                return urlParams.get('id');
            }

            async init() {
                if (!this.venueId) {
                    this.showError('No venue ID provided');
                    return;
                }
                
                // Load event modal component
                await this.loadEventModal();
                
                // Load venue data
                await this.loadVenue();
                await this.loadVenueEvents();
            }

            async loadEventModal() {
                try {
                    const response = await fetch('/components/eventDetailModal.html');
                    const modalHTML = await response.text();
                    document.getElementById('eventModalContainer').innerHTML = modalHTML;
                } catch (error) {
                    console.error('Error loading event modal:', error);
                }
            }

            async loadVenue() {
                try {
                    // First try to get from enhanced venues
                    let response = await fetch(`${this.apiBaseUrl}/api/enhanced/venues/${this.venueId}`);
                    let data = await response.json();

                    if (data.success) {
                        this.venue = data.venue;
                    } else {
                        // Fallback: try to get from priority venues database
                        response = await fetch(`${this.apiBaseUrl}/api/venues/${this.venueId}`);
                        data = await response.json();
                        
                        if (data.success) {
                            this.venue = data.venue;
                        } else {
                            throw new Error('Venue not found');
                        }
                    }

                    this.renderVenue();
                    document.title = `${this.venue.name} - Southeast Texas Events`;

                } catch (error) {
                    console.error('Error loading venue:', error);
                    this.showError('Error loading venue: ' + error.message);
                }
            }

            async loadVenueEvents() {
                try {
                    const response = await fetch(`${this.apiBaseUrl}/api/enhanced/events?venue=${this.venueId}`);
                    const data = await response.json();

                    if (data.success) {
                        this.events = data.events;
                        this.filteredEvents = [...this.events];
                        this.renderEvents();
                    } else {
                        console.log('No events found for this venue');
                        this.events = [];
                        this.filteredEvents = [];
                        this.renderEvents();
                    }

                } catch (error) {
                    console.error('Error loading venue events:', error);
                    this.renderEvents(); // Render empty state
                }
            }

            renderVenue() {
                const html = `
                    <div class="venue-hero">
                        <div class="venue-header">
                            <div class="venue-info">
                                <h1 class="venue-name">${this.venue.name}</h1>
                                <div class="venue-type">${this.venue.type?.replace('-', ' ') || 'Venue'}</div>
                                <p style="color: #666; font-size: 1.1em; margin-bottom: 20px;">
                                    ${this.venue.description || 'Premier venue in Southeast Texas hosting various events and activities.'}
                                </p>
                            </div>
                            
                            <div class="venue-stats">
                                <div class="stat-item">
                                    <span class="stat-label">Capacity</span>
                                    <span class="stat-value">${this.venue.capacity || 'N/A'}</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">Events</span>
                                    <span class="stat-value">${this.events.length}</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">Type</span>
                                    <span class="stat-value">${this.venue.type?.replace('-', ' ') || 'Venue'}</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">Status</span>
                                    <span class="stat-value">${this.venue.isVerified ? '✅ Verified' : '⏳ Pending'}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="venue-details-grid">
                        <div class="detail-section">
                            <h3><span class="section-icon">📍</span>Location & Contact</h3>
                            <div class="contact-item">
                                <div class="contact-icon">📍</div>
                                <div class="contact-info">
                                    <div><strong>Address</strong></div>
                                    <div>${this.venue.address}</div>
                                </div>
                            </div>
                            
                            ${this.venue.phone ? `
                            <div class="contact-item">
                                <div class="contact-icon">📞</div>
                                <div class="contact-info">
                                    <div><strong>Phone</strong></div>
                                    <div><a href="tel:${this.venue.phone}">${this.venue.phone}</a></div>
                                </div>
                            </div>
                            ` : ''}
                            
                            ${this.venue.email ? `
                            <div class="contact-item">
                                <div class="contact-icon">✉️</div>
                                <div class="contact-info">
                                    <div><strong>Email</strong></div>
                                    <div><a href="mailto:${this.venue.email}">${this.venue.email}</a></div>
                                </div>
                            </div>
                            ` : ''}
                            
                            ${this.venue.website ? `
                            <div class="contact-item">
                                <div class="contact-icon">🌐</div>
                                <div class="contact-info">
                                    <div><strong>Website</strong></div>
                                    <div><a href="${this.venue.website}" target="_blank">Visit Website</a></div>
                                </div>
                            </div>
                            ` : ''}
                        </div>

                        ${this.venue.amenities && this.venue.amenities.length > 0 ? `
                        <div class="detail-section">
                            <h3><span class="section-icon">✨</span>Amenities & Features</h3>
                            <div class="amenities-grid">
                                ${this.venue.amenities.map(amenity => `
                                    <div class="amenity-item">
                                        <div class="amenity-icon">${this.getAmenityIcon(amenity)}</div>
                                        <div class="amenity-name">${amenity.replace('-', ' ')}</div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                        ` : ''}
                    </div>

                    <div class="events-section">
                        <div class="events-header">
                            <h2>📅 Events at ${this.venue.name}</h2>
                            <div class="events-controls" style="display: flex; gap: 15px; align-items: center;">
                                <button onclick="venuePage.scrapeVenueEvents()"
                                        style="background: #28a745; color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer; font-size: 14px;">
                                    🌐 Get Real Events
                                </button>
                                <select id="categoryFilter" onchange="venuePage.filterEvents()">
                                    <option value="">All Categories</option>
                                    <option value="business">Business</option>
                                    <option value="entertainment">Entertainment</option>
                                    <option value="education">Education</option>
                                    <option value="sports">Sports</option>
                                    <option value="community">Community</option>
                                    <option value="arts">Arts & Culture</option>
                                </select>
                            </div>
                        </div>
                        <div id="eventsGrid" class="events-grid">
                            <!-- Events will be rendered here -->
                        </div>
                    </div>
                `;

                document.getElementById('venueContent').innerHTML = html;
            }

            renderEvents() {
                const grid = document.getElementById('eventsGrid');
                
                if (!grid) return;

                if (this.filteredEvents.length === 0) {
                    grid.innerHTML = `
                        <div class="no-events">
                            <h3>No events found</h3>
                            <p>There are currently no events scheduled at this venue.</p>
                            <p>Check back later or contact the venue directly for more information.</p>
                        </div>
                    `;
                    return;
                }

                grid.innerHTML = this.filteredEvents.map(event => this.createEventCard(event)).join('');
            }

            createEventCard(event) {
                const date = new Date(event.originalEvent?.date || event.date);
                const formattedDate = date.toLocaleDateString('en-US', {
                    weekday: 'short',
                    month: 'short',
                    day: 'numeric'
                });

                return `
                    <div class="event-card" onclick="venuePage.showEventDetails('${event.id}')">
                        <div class="event-date">${formattedDate}</div>
                        <div class="event-title">${event.originalEvent?.title || event.title}</div>
                        <div class="event-description">${event.originalEvent?.description || event.description}</div>
                        <div class="event-meta">
                            <span class="event-category">${event.originalEvent?.category || event.category}</span>
                            <span>${event.extractedPrice || event.price || 'Price TBD'}</span>
                        </div>
                    </div>
                `;
            }

            filterEvents() {
                const categoryFilter = document.getElementById('categoryFilter').value;

                this.filteredEvents = this.events.filter(event => {
                    const matchesCategory = !categoryFilter || 
                        (event.originalEvent?.category === categoryFilter) ||
                        (event.category === categoryFilter);
                    
                    return matchesCategory;
                });

                this.renderEvents();
            }

            async scrapeVenueEvents() {
                const button = event.target;
                const originalText = button.innerHTML;

                try {
                    // Update button to show loading
                    button.innerHTML = '⏳ Scraping...';
                    button.disabled = true;

                    // Call the venue-specific scraping API
                    const response = await fetch(`${this.apiBaseUrl}/api/venues/${this.venueId}/scrape`, {
                        method: 'POST'
                    });

                    const data = await response.json();

                    if (data.success) {
                        // Reload events after successful scraping
                        await this.loadVenueEvents();

                        // Show success message
                        button.innerHTML = '✅ Success!';
                        setTimeout(() => {
                            button.innerHTML = originalText;
                            button.disabled = false;
                        }, 2000);
                    } else {
                        throw new Error(data.message || 'Scraping failed');
                    }

                } catch (error) {
                    console.error('Error scraping venue events:', error);
                    button.innerHTML = '❌ Failed';
                    setTimeout(() => {
                        button.innerHTML = originalText;
                        button.disabled = false;
                    }, 2000);
                }
            }

            showEventDetails(eventId) {
                const event = this.events.find(e => e.id === eventId);
                if (event && window.eventDetailModal) {
                    window.eventDetailModal.show(event);
                }
            }

            getAmenityIcon(amenity) {
                const icons = {
                    'parking': '🚗',
                    'wheelchair-accessible': '♿',
                    'concessions': '🍿',
                    'wifi': '📶',
                    'air-conditioning': '❄️',
                    'outdoor': '🌳',
                    'family-friendly': '👨‍👩‍👧‍👦',
                    'educational': '📚',
                    'food-service': '🍽️',
                    'gift-shop': '🛍️',
                    'multiple-venues': '🏢',
                    'hotel': '🏨',
                    'kitchen': '👨‍🍳',
                    'historic': '🏛️',
                    'tours': '🚶‍♂️',
                    'gardens': '🌺',
                    'picnic-areas': '🧺',
                    'trails': '🥾',
                    'student-discounts': '🎓'
                };
                return icons[amenity] || '✨';
            }

            showError(message) {
                document.getElementById('venueContent').innerHTML = `
                    <div class="error">
                        <h2>❌ Error</h2>
                        <p>${message}</p>
                        <a href="/" style="color: #721c24; font-weight: bold; margin-top: 15px; display: inline-block;">← Back to Events</a>
                    </div>
                `;
            }
        }

        // Initialize page
        let venuePage;
        document.addEventListener('DOMContentLoaded', () => {
            venuePage = new VenuePage();
        });
    </script>
</body>
</html>
