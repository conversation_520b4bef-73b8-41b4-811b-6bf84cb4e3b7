<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug - Southeast Texas Events</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .debug-section {
            background: white;
            padding: 20px;
            margin: 10px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .event-card {
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .event-title {
            font-size: 1.2em;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .event-date {
            color: #666;
            margin-bottom: 5px;
        }
        .event-location {
            color: #888;
            margin-bottom: 10px;
        }
        .event-link {
            display: inline-block;
            background: #007bff;
            color: white;
            padding: 8px 16px;
            text-decoration: none;
            border-radius: 4px;
            margin-top: 10px;
        }
        .event-link:hover {
            background: #0056b3;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.loading { background: #d1ecf1; color: #0c5460; }
        button {
            background: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #218838;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <h1>🔧 Southeast Texas Events - Debug Page</h1>
    
    <div class="debug-section">
        <h2>🔍 API Tests</h2>
        <button onclick="testAPI()">Test API Connection</button>
        <button onclick="loadEvents()">Load Events</button>
        <button onclick="triggerScraping()">Trigger Scraping</button>
        <div id="apiStatus"></div>
    </div>

    <div class="debug-section">
        <h2>📊 Event Data</h2>
        <div id="eventCount">Events loaded: 0</div>
        <div id="eventData"></div>
    </div>

    <div class="debug-section">
        <h2>📅 Events Display</h2>
        <div id="eventsContainer"></div>
    </div>

    <script>
        let events = [];
        const apiBaseUrl = window.location.origin;

        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('apiStatus');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        async function testAPI() {
            try {
                showStatus('Testing API connection...', 'loading');
                
                const response = await fetch(`${apiBaseUrl}/api/test`);
                const data = await response.json();
                
                if (data.success) {
                    showStatus(`✅ API Connected: ${data.message}`, 'success');
                } else {
                    showStatus('❌ API test failed', 'error');
                }
            } catch (error) {
                showStatus(`❌ API Error: ${error.message}`, 'error');
                console.error('API Test Error:', error);
            }
        }

        async function loadEvents() {
            try {
                showStatus('Loading events from API...', 'loading');
                
                const response = await fetch(`${apiBaseUrl}/api/events`);
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const responseText = await response.text();
                console.log('Raw response:', responseText.substring(0, 500));
                
                const data = JSON.parse(responseText);
                console.log('Parsed data:', data);
                
                if (data.success) {
                    events = data.events;
                    document.getElementById('eventCount').textContent = `Events loaded: ${events.length}`;
                    
                    // Show raw data
                    document.getElementById('eventData').innerHTML = `
                        <pre>${JSON.stringify(data, null, 2).substring(0, 1000)}...</pre>
                    `;
                    
                    displayEvents();
                    showStatus(`✅ Loaded ${events.length} events successfully`, 'success');
                } else {
                    throw new Error(data.error || 'Failed to load events');
                }
                
            } catch (error) {
                showStatus(`❌ Load Error: ${error.message}`, 'error');
                console.error('Load Events Error:', error);
            }
        }

        async function triggerScraping() {
            try {
                showStatus('Triggering web scraping...', 'loading');
                
                const response = await fetch(`${apiBaseUrl}/api/events/scrape`, {
                    method: 'POST'
                });
                const data = await response.json();
                
                if (data.success) {
                    showStatus(`✅ Scraping started: ${data.message}`, 'success');
                    
                    // Wait a bit then reload events
                    setTimeout(() => {
                        loadEvents();
                    }, 5000);
                } else {
                    throw new Error(data.error || 'Failed to start scraping');
                }
                
            } catch (error) {
                showStatus(`❌ Scraping Error: ${error.message}`, 'error');
                console.error('Scraping Error:', error);
            }
        }

        function displayEvents() {
            const container = document.getElementById('eventsContainer');
            
            if (events.length === 0) {
                container.innerHTML = '<div class="status error">No events to display</div>';
                return;
            }
            
            container.innerHTML = events.slice(0, 10).map(event => {
                const date = new Date(event.date);
                const formattedDate = date.toLocaleDateString('en-US', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                });
                
                return `
                    <div class="event-card">
                        <div class="event-title">${event.title}</div>
                        <div class="event-date">📅 ${formattedDate}</div>
                        <div class="event-location">📍 ${event.location}</div>
                        <div class="event-description">${event.description}</div>
                        <div class="event-organizer">🏢 ${event.organizer}</div>
                        <a href="${event.url}" target="_blank" class="event-link">View Event Details →</a>
                    </div>
                `;
            }).join('');
        }

        // Auto-run tests when page loads
        window.addEventListener('load', () => {
            console.log('🔧 Debug page loaded');
            testAPI();
            setTimeout(() => {
                loadEvents();
            }, 1000);
        });
    </script>
</body>
</html>
