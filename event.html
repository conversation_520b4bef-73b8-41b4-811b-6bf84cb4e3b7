<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Event Details - Southeast Texas Events</title>
    <!-- Include Event Detail Modal -->
    <link rel="stylesheet" href="components/eventDetailModal.html">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            font-size: 1.8em;
            font-weight: 600;
        }
        
        .nav-links {
            display: flex;
            gap: 20px;
        }
        
        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 6px;
            transition: background 0.3s ease;
        }
        
        .nav-links a:hover {
            background: rgba(255,255,255,0.2);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px 20px;
        }
        
        .event-header {
            background: white;
            border-radius: 15px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }
        
        .event-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .event-status {
            position: absolute;
            top: 20px;
            right: 20px;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: bold;
        }
        
        .status-verified {
            background: #d4edda;
            color: #155724;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .event-title {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 20px;
            margin-right: 120px;
            color: #333;
            line-height: 1.2;
        }
        
        .event-meta {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .meta-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        
        .meta-icon {
            font-size: 1.2em;
            width: 24px;
            text-align: center;
        }
        
        .meta-content {
            flex: 1;
        }
        
        .meta-label {
            font-size: 0.85em;
            color: #666;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .meta-value {
            font-size: 1.1em;
            color: #333;
            font-weight: 600;
        }
        
        .event-description {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        }
        
        .event-description h2 {
            margin-bottom: 20px;
            color: #333;
            font-size: 1.5em;
        }
        
        .event-description p {
            color: #555;
            font-size: 1.1em;
            line-height: 1.7;
            margin-bottom: 15px;
        }
        
        .event-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 20px;
        }
        
        .tag {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 6px 14px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 500;
        }
        
        .venue-section {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        }
        
        .venue-section h2 {
            margin-bottom: 20px;
            color: #333;
            font-size: 1.5em;
        }
        
        .venue-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }
        
        .venue-details {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .venue-detail {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .venue-map {
            background: #e9ecef;
            border-radius: 10px;
            height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-style: italic;
        }
        
        .action-buttons {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            text-align: center;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 0 10px 10px 0;
            border: none;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            font-size: 1em;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        
        .btn-outline {
            background: transparent;
            color: #667eea;
            border: 2px solid #667eea;
        }
        
        .btn-outline:hover {
            background: #667eea;
            color: white;
        }
        
        .loading {
            text-align: center;
            padding: 60px;
            color: #666;
            font-size: 1.2em;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin: 20px 0;
        }
        
        @media (max-width: 768px) {
            .event-title {
                font-size: 2em;
                margin-right: 0;
            }
            
            .event-meta {
                grid-template-columns: 1fr;
            }
            
            .venue-info {
                grid-template-columns: 1fr;
            }
            
            .header-content {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }
            
            .nav-links {
                flex-wrap: wrap;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <h1>🎯 Southeast Texas Events</h1>
            <div class="nav-links">
                <a href="/">Home</a>
                <a href="/admin.html">Admin</a>
                <a href="#share">Share</a>
            </div>
        </div>
    </div>

    <div class="container">
        <div id="eventContent">
            <div class="loading">Loading event details...</div>
        </div>
    </div>

    <script>
        class EventPage {
            constructor() {
                this.apiBaseUrl = window.location.origin;
                this.eventId = this.getEventIdFromUrl();
                this.init();
            }

            getEventIdFromUrl() {
                const urlParams = new URLSearchParams(window.location.search);
                return urlParams.get('id');
            }

            init() {
                if (!this.eventId) {
                    this.showError('No event ID provided');
                    return;
                }
                
                this.loadEvent();
            }

            async loadEvent() {
                try {
                    const response = await fetch(`${this.apiBaseUrl}/api/enhanced/events/${this.eventId}`);
                    const data = await response.json();

                    if (data.success) {
                        this.renderEvent(data.event);
                        document.title = `${data.event.originalEvent.title} - Southeast Texas Events`;
                    } else {
                        throw new Error(data.error || 'Event not found');
                    }
                } catch (error) {
                    console.error('Error loading event:', error);
                    this.showError('Error loading event: ' + error.message);
                }
            }

            renderEvent(event) {
                const date = new Date(event.originalEvent.date);
                const formattedDate = date.toLocaleDateString('en-US', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                });
                const formattedTime = date.toLocaleTimeString('en-US', {
                    hour: 'numeric',
                    minute: '2-digit'
                });

                const html = `
                    <div class="event-header">
                        <div class="event-status ${event.isVerified ? 'status-verified' : 'status-pending'}">
                            ${event.isVerified ? '✓ Verified' : '⏳ Pending'}
                        </div>
                        <h1 class="event-title">${event.originalEvent.title}</h1>
                        
                        <div class="event-meta">
                            <div class="meta-item">
                                <div class="meta-icon">📅</div>
                                <div class="meta-content">
                                    <div class="meta-label">Date</div>
                                    <div class="meta-value">${formattedDate}</div>
                                </div>
                            </div>
                            
                            <div class="meta-item">
                                <div class="meta-icon">⏰</div>
                                <div class="meta-content">
                                    <div class="meta-label">Time</div>
                                    <div class="meta-value">${formattedTime}</div>
                                </div>
                            </div>
                            
                            <div class="meta-item">
                                <div class="meta-icon">📍</div>
                                <div class="meta-content">
                                    <div class="meta-label">Location</div>
                                    <div class="meta-value">${event.extractedVenueName || event.originalEvent.location}</div>
                                </div>
                            </div>
                            
                            <div class="meta-item">
                                <div class="meta-icon">🏢</div>
                                <div class="meta-content">
                                    <div class="meta-label">Organizer</div>
                                    <div class="meta-value">${event.originalEvent.organizer}</div>
                                </div>
                            </div>
                            
                            ${event.extractedPrice ? `
                            <div class="meta-item">
                                <div class="meta-icon">💰</div>
                                <div class="meta-content">
                                    <div class="meta-label">Price</div>
                                    <div class="meta-value">${event.extractedPrice}</div>
                                </div>
                            </div>
                            ` : ''}
                            
                            <div class="meta-item">
                                <div class="meta-icon">🏷️</div>
                                <div class="meta-content">
                                    <div class="meta-label">Category</div>
                                    <div class="meta-value">${event.originalEvent.category}</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="event-description">
                        <h2>📝 Event Description</h2>
                        <p>${event.customDescription || event.originalEvent.description}</p>
                        
                        ${event.extractedTags.length > 0 ? `
                        <div class="event-tags">
                            ${event.extractedTags.map(tag => `<span class="tag">${tag}</span>`).join('')}
                        </div>
                        ` : ''}
                    </div>

                    ${event.venue ? this.renderVenueSection(event.venue) : ''}

                    <div class="action-buttons">
                        <h2 style="margin-bottom: 20px;">🎯 Event Actions</h2>
                        <a href="${event.originalEvent.url}" target="_blank" class="btn btn-primary">
                            🔗 View Original Event
                        </a>
                        <button class="btn btn-outline" onclick="shareEvent()">
                            📤 Share Event
                        </button>
                        <button class="btn btn-outline" onclick="addToCalendar()">
                            📅 Add to Calendar
                        </button>
                        ${event.extractedPhone ? `
                        <a href="tel:${event.extractedPhone}" class="btn btn-outline">
                            📞 Call ${event.extractedPhone}
                        </a>
                        ` : ''}
                        ${event.extractedEmail ? `
                        <a href="mailto:${event.extractedEmail}" class="btn btn-outline">
                            ✉️ Email Organizer
                        </a>
                        ` : ''}
                    </div>
                `;

                document.getElementById('eventContent').innerHTML = html;
            }

            renderVenueSection(venue) {
                return `
                    <div class="venue-section">
                        <h2>🏢 Venue Information</h2>
                        <div class="venue-info">
                            <div class="venue-details">
                                <div class="venue-detail">
                                    <div class="meta-icon">🏢</div>
                                    <div>
                                        <div class="meta-label">Venue Name</div>
                                        <div class="meta-value">${venue.name}</div>
                                    </div>
                                </div>
                                
                                <div class="venue-detail">
                                    <div class="meta-icon">📍</div>
                                    <div>
                                        <div class="meta-label">Address</div>
                                        <div class="meta-value">${venue.address}</div>
                                    </div>
                                </div>
                                
                                ${venue.phone ? `
                                <div class="venue-detail">
                                    <div class="meta-icon">📞</div>
                                    <div>
                                        <div class="meta-label">Phone</div>
                                        <div class="meta-value">${venue.phone}</div>
                                    </div>
                                </div>
                                ` : ''}
                                
                                ${venue.website ? `
                                <div class="venue-detail">
                                    <div class="meta-icon">🌐</div>
                                    <div>
                                        <div class="meta-label">Website</div>
                                        <div class="meta-value">
                                            <a href="${venue.website}" target="_blank">${venue.website}</a>
                                        </div>
                                    </div>
                                </div>
                                ` : ''}
                                
                                ${venue.parking ? `
                                <div class="venue-detail">
                                    <div class="meta-icon">🚗</div>
                                    <div>
                                        <div class="meta-label">Parking</div>
                                        <div class="meta-value">${venue.parking}</div>
                                    </div>
                                </div>
                                ` : ''}
                            </div>
                            
                            <div class="venue-map">
                                📍 Map integration coming soon<br>
                                <small>${venue.address}</small>
                            </div>
                        </div>
                    </div>
                `;
            }

            showError(message) {
                document.getElementById('eventContent').innerHTML = `
                    <div class="error">
                        <h2>❌ Error</h2>
                        <p>${message}</p>
                        <a href="/" class="btn btn-primary" style="margin-top: 15px;">← Back to Events</a>
                    </div>
                `;
            }
        }

        // Global functions
        function shareEvent() {
            if (navigator.share) {
                navigator.share({
                    title: document.title,
                    url: window.location.href
                });
            } else {
                // Fallback: copy to clipboard
                navigator.clipboard.writeText(window.location.href).then(() => {
                    alert('Event link copied to clipboard!');
                });
            }
        }

        function addToCalendar() {
            // TODO: Implement calendar integration
            alert('Calendar integration coming soon!');
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', () => {
            new EventPage();
        });
    </script>
</body>
</html>
