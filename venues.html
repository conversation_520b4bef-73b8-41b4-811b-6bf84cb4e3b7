<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Entertainment Venues - Southeast Texas Events</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            color: #333;
            line-height: 1.6;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px 0;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 3em;
            font-weight: bold;
            margin-bottom: 15px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
            margin-bottom: 30px;
        }
        
        .nav-links {
            display: flex;
            justify-content: center;
            gap: 30px;
            flex-wrap: wrap;
        }
        
        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 12px 24px;
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 25px;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .nav-links a:hover {
            background: rgba(255,255,255,0.2);
            border-color: rgba(255,255,255,0.6);
            transform: translateY(-2px);
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .filters-section {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .filter-group label {
            font-weight: 600;
            color: #555;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .filter-group select,
        .filter-group input {
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 1em;
            transition: border-color 0.3s ease;
        }
        
        .filter-group select:focus,
        .filter-group input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .stats-bar {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .stat-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9em;
            color: #666;
        }
        
        .stat-value {
            font-weight: bold;
            color: #667eea;
        }
        
        .venues-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
            gap: 30px;
        }
        
        .venue-card {
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .venue-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 50px rgba(0,0,0,0.15);
        }
        
        .venue-header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 25px;
            position: relative;
        }
        
        .venue-priority {
            position: absolute;
            top: 15px;
            right: 15px;
            background: rgba(255,255,255,0.2);
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: bold;
        }
        
        .venue-name {
            font-size: 1.4em;
            font-weight: bold;
            margin-bottom: 8px;
            margin-right: 80px;
        }
        
        .venue-type {
            background: rgba(255,255,255,0.2);
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 0.85em;
            display: inline-block;
            margin-bottom: 10px;
            text-transform: capitalize;
        }
        
        .venue-location {
            font-size: 0.9em;
            opacity: 0.9;
        }
        
        .venue-body {
            padding: 25px;
        }
        
        .venue-description {
            color: #666;
            margin-bottom: 20px;
            line-height: 1.6;
        }
        
        .venue-features {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9em;
        }
        
        .feature-icon {
            color: #667eea;
            font-weight: bold;
        }
        
        .venue-specialties {
            margin-bottom: 20px;
        }
        
        .specialties-title {
            font-size: 0.9em;
            font-weight: 600;
            color: #555;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .specialties-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        
        .specialty-tag {
            background: #e9ecef;
            color: #495057;
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 500;
        }
        
        .food-events-section {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .food-events-title {
            color: #856404;
            font-weight: 600;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .food-events-list {
            color: #856404;
            font-size: 0.9em;
            line-height: 1.5;
        }
        
        .venue-actions {
            display: flex;
            gap: 10px;
            justify-content: space-between;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            flex: 1;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        
        .btn-outline {
            background: transparent;
            color: #667eea;
            border: 2px solid #667eea;
        }
        
        .btn-outline:hover {
            background: #667eea;
            color: white;
        }
        
        .loading {
            text-align: center;
            padding: 60px;
            color: #666;
            font-size: 1.2em;
        }
        
        .no-results {
            text-align: center;
            padding: 60px;
            color: #666;
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2.2em;
            }
            
            .venues-grid {
                grid-template-columns: 1fr;
            }
            
            .filters-grid {
                grid-template-columns: 1fr;
            }
            
            .venue-features {
                grid-template-columns: 1fr;
            }
            
            .venue-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <h1>🏢 Entertainment Venues</h1>
            <p>Discover Southeast Texas's Premier Event Venues & Food Destinations</p>
            <div class="nav-links">
                <a href="/">🏠 Home</a>
                <a href="/admin.html">⚙️ Admin</a>
                <a href="#food-venues">🍽️ Food Events</a>
                <a href="#weddings">💒 Weddings</a>
                <a href="#entertainment">🎭 Entertainment</a>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="filters-section">
            <div class="filters-grid">
                <div class="filter-group">
                    <label>Search Venues</label>
                    <input type="text" id="searchInput" placeholder="Search by name, type, or specialty...">
                </div>
                <div class="filter-group">
                    <label>City</label>
                    <select id="cityFilter">
                        <option value="">All Cities</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label>Venue Type</label>
                    <select id="typeFilter">
                        <option value="">All Types</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label>Capacity Range</label>
                    <select id="capacityFilter">
                        <option value="">Any Size</option>
                        <option value="intimate">Intimate (0-100)</option>
                        <option value="medium">Medium (101-300)</option>
                        <option value="large">Large (301-1000)</option>
                        <option value="massive">Massive (1000+)</option>
                    </select>
                </div>
            </div>
            
            <div class="stats-bar">
                <div class="stat-item">
                    <span>📊 Total Venues:</span>
                    <span class="stat-value" id="totalVenues">0</span>
                </div>
                <div class="stat-item">
                    <span>🍽️ Food Event Venues:</span>
                    <span class="stat-value" id="foodVenues">0</span>
                </div>
                <div class="stat-item">
                    <span>🏙️ Cities Covered:</span>
                    <span class="stat-value" id="citiesCount">0</span>
                </div>
                <div class="stat-item">
                    <span>👥 Average Capacity:</span>
                    <span class="stat-value" id="avgCapacity">0</span>
                </div>
            </div>
        </div>

        <div id="venuesContainer">
            <div class="loading">Loading venues...</div>
        </div>
    </div>

    <script>
        class VenuesShowcase {
            constructor() {
                this.venues = [];
                this.filteredVenues = [];
                this.apiBaseUrl = window.location.origin;
                this.init();
            }

            async init() {
                await this.loadVenues();
                this.setupFilters();
                this.renderVenues();
                this.updateStats();
            }

            async loadVenues() {
                try {
                    const response = await fetch(`${this.apiBaseUrl}/api/enhanced/priority-venues`);
                    const data = await response.json();
                    
                    if (data.success) {
                        this.venues = data.venues;
                        this.filteredVenues = [...this.venues];
                        this.populateFilterOptions();
                    } else {
                        throw new Error('Failed to load venues');
                    }
                } catch (error) {
                    console.error('Error loading venues:', error);
                    document.getElementById('venuesContainer').innerHTML = `
                        <div class="no-results">
                            <h3>❌ Error Loading Venues</h3>
                            <p>Unable to load venue information. Please try again later.</p>
                        </div>
                    `;
                }
            }

            populateFilterOptions() {
                // Populate city filter
                const cities = [...new Set(this.venues.map(v => v.city))].sort();
                const cityFilter = document.getElementById('cityFilter');
                cities.forEach(city => {
                    const option = document.createElement('option');
                    option.value = city;
                    option.textContent = city;
                    cityFilter.appendChild(option);
                });

                // Populate type filter
                const types = [...new Set(this.venues.map(v => v.type))].sort();
                const typeFilter = document.getElementById('typeFilter');
                types.forEach(type => {
                    const option = document.createElement('option');
                    option.value = type;
                    option.textContent = type.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase());
                    typeFilter.appendChild(option);
                });
            }

            setupFilters() {
                const searchInput = document.getElementById('searchInput');
                const cityFilter = document.getElementById('cityFilter');
                const typeFilter = document.getElementById('typeFilter');
                const capacityFilter = document.getElementById('capacityFilter');

                [searchInput, cityFilter, typeFilter, capacityFilter].forEach(element => {
                    element.addEventListener('input', () => this.applyFilters());
                });
            }

            applyFilters() {
                const search = document.getElementById('searchInput').value.toLowerCase();
                const city = document.getElementById('cityFilter').value;
                const type = document.getElementById('typeFilter').value;
                const capacity = document.getElementById('capacityFilter').value;

                this.filteredVenues = this.venues.filter(venue => {
                    const matchesSearch = !search || 
                        venue.name.toLowerCase().includes(search) ||
                        venue.type.toLowerCase().includes(search) ||
                        (venue.description && venue.description.toLowerCase().includes(search)) ||
                        (venue.specialties && venue.specialties.some(s => s.toLowerCase().includes(search)));

                    const matchesCity = !city || venue.city === city;
                    const matchesType = !type || venue.type === type;
                    
                    let matchesCapacity = true;
                    if (capacity) {
                        switch (capacity) {
                            case 'intimate': matchesCapacity = venue.capacity <= 100; break;
                            case 'medium': matchesCapacity = venue.capacity > 100 && venue.capacity <= 300; break;
                            case 'large': matchesCapacity = venue.capacity > 300 && venue.capacity <= 1000; break;
                            case 'massive': matchesCapacity = venue.capacity > 1000; break;
                        }
                    }

                    return matchesSearch && matchesCity && matchesType && matchesCapacity;
                });

                this.renderVenues();
                this.updateStats();
            }

            renderVenues() {
                const container = document.getElementById('venuesContainer');
                
                if (this.filteredVenues.length === 0) {
                    container.innerHTML = `
                        <div class="no-results">
                            <h3>🔍 No Venues Found</h3>
                            <p>Try adjusting your filters to see more results.</p>
                        </div>
                    `;
                    return;
                }

                const venuesHTML = this.filteredVenues.map(venue => this.createVenueCard(venue)).join('');
                container.innerHTML = `<div class="venues-grid">${venuesHTML}</div>`;
            }

            createVenueCard(venue) {
                const foodEventsSection = venue.foodEvents && venue.foodEvents.length > 0 ? `
                    <div class="food-events-section">
                        <div class="food-events-title">
                            🍽️ Food Events & Catering
                        </div>
                        <div class="food-events-list">
                            ${venue.foodEvents.map(event => `• ${event}`).join('<br>')}
                        </div>
                    </div>
                ` : '';

                const specialtiesSection = venue.specialties && venue.specialties.length > 0 ? `
                    <div class="venue-specialties">
                        <div class="specialties-title">Specialties</div>
                        <div class="specialties-tags">
                            ${venue.specialties.map(specialty => 
                                `<span class="specialty-tag">${specialty.replace('-', ' ')}</span>`
                            ).join('')}
                        </div>
                    </div>
                ` : '';

                return `
                    <div class="venue-card" onclick="venuesApp.openVenuePage('${venue.id}')">
                        <div class="venue-header">
                            <div class="venue-priority">Priority ${venue.priority}</div>
                            <div class="venue-name">${venue.name}</div>
                            <div class="venue-type">${venue.type.replace('-', ' ')}</div>
                            <div class="venue-location">📍 ${venue.address}</div>
                        </div>
                        
                        <div class="venue-body">
                            <div class="venue-description">
                                ${venue.description || 'Premier venue in Southeast Texas.'}
                            </div>
                            
                            <div class="venue-features">
                                <div class="feature-item">
                                    <span class="feature-icon">👥</span>
                                    <span>Capacity: ${venue.capacity}</span>
                                </div>
                                <div class="feature-item">
                                    <span class="feature-icon">📞</span>
                                    <span>${venue.phone}</span>
                                </div>
                                <div class="feature-item">
                                    <span class="feature-icon">🏙️</span>
                                    <span>${venue.city}</span>
                                </div>
                                <div class="feature-item">
                                    <span class="feature-icon">🌐</span>
                                    <span>Website Available</span>
                                </div>
                            </div>
                            
                            ${specialtiesSection}
                            ${foodEventsSection}
                            
                            <div class="venue-actions">
                                <a href="/venue.html?id=${venue.id}" class="btn btn-primary">
                                    View Details
                                </a>
                                <a href="${venue.website}" target="_blank" class="btn btn-outline">
                                    Visit Website
                                </a>
                            </div>
                        </div>
                    </div>
                `;
            }

            updateStats() {
                document.getElementById('totalVenues').textContent = this.filteredVenues.length;
                document.getElementById('foodVenues').textContent = 
                    this.filteredVenues.filter(v => v.foodEvents && v.foodEvents.length > 0).length;
                document.getElementById('citiesCount').textContent = 
                    new Set(this.filteredVenues.map(v => v.city)).size;
                
                const avgCapacity = this.filteredVenues.length > 0 ? 
                    Math.round(this.filteredVenues.reduce((sum, v) => sum + v.capacity, 0) / this.filteredVenues.length) : 0;
                document.getElementById('avgCapacity').textContent = avgCapacity;
            }

            openVenuePage(venueId) {
                window.location.href = `/venue.html?id=${venueId}`;
            }
        }

        // Initialize the application
        let venuesApp;
        document.addEventListener('DOMContentLoaded', () => {
            venuesApp = new VenuesShowcase();
        });
    </script>
</body>
</html>
